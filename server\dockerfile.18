# FROM node:18-bullseye-slim
# # FROM denoland/deno:2.3.1

# # get from archive
# # RUN echo "deb http://archive.debian.org/debian stretch main contrib non-free" > /etc/apt/sources.list

# # install libre
# WORKDIR /tmp
# RUN apt update \
#     && apt install -y libxinerama1 libfontconfig1 libdbus-glib-1-2 libcairo2 libcups2 libglu1-mesa libsm6 unzip wget
# # RUN wget http://downloadarchive.documentfoundation.org/libreoffice/old/*******/deb/x86_64/LibreOffice_*******_Linux_x86-64_deb.tar.gz -O libo.tar.gz
# #RUN wget --no-check-certificate http://downloadarchive.documentfoundation.org/libreoffice/old/*******/deb/x86_64/LibreOffice_*******_Linux_x86-64_deb.tar.gz -O libo.tar.gz
# RUN wget --no-check-certificate http://downloadarchive.documentfoundation.org/libreoffice/old/********/deb/x86_64/LibreOffice_********_Linux_x86-64_deb.tar.gz -O libo.tar.gz
# RUN tar -zxvf libo.tar.gz
# WORKDIR /tmp/LibreOffice_********_Linux_x86-64_deb/DEBS
# RUN dpkg -i *.deb

# # needed for whatsapp-web
# RUN apt update && apt install -y libnss3 libatk-adaptor libcups2 libxkbcommon0 libgtk-3-0 libgbm1 libasound2 build-essential
# # install ghostscript
# WORKDIR /tmp
# RUN wget https://github.com/ArtifexSoftware/ghostpdl-downloads/releases/download/gs10021/ghostscript-10.02.1.tar.gz -O gs.tar.gz
# RUN tar -zxvf gs.tar.gz
# WORKDIR /tmp/ghostscript-10.02.1
# RUN ./configure
# RUN make && make install
# # RUN apt install -y ghostscript
# RUN rm -rf /tmp/*

# RUN apt install -y default-jre

# RUN npm install -g nodemon

# ====================================

FROM indrawan/server:18-base
WORKDIR /app
COPY package.json /app
RUN yarn install
RUN npm install --os=linux --cpu=x64 sharp
RUN npm install --include=optional sharp

# RUN yarn upgrade whatsapp-web.js

# CMD [ "nodemon", "--ignore", "'uploads/*'", "--ignore","'node_modules/*'", "--ignore", "'tmp/*'", "app.js" ]
# CMD npm install && nodemon app.js

# ====================================

# FROM indrawan/server:deno-base
# WORKDIR /app
# COPY . /app
# CMD [ "deno", "--allow-net", "--allow-read", "--allow-write", "--allow-env", "app.js" ]
