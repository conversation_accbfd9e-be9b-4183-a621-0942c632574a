// Telegram Bot Configuration
const ENCKEY = process.env.ENCRYPTION_KEY || '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5'
module.exports = {
  // Bot settings
  BOT_TOKEN: process.env.TELEGRAM_BOT_TOKEN || '**********************************************',
  ADMIN_ID: process.env.TELEGRAM_ADMIN_ID || '662216470',
  
  // Message limits
  MAX_MESSAGE_LENGTH: 4096,
  MAX_LOG_LENGTH: 4000,
  
  // Session settings
  SESSION_TTL: 30 * 60 * 1000, // 30 minutes
  
  // Validation patterns
  PATTERNS: {
    NIK: /^\d{16}$/,
    REKOM_NUMBER: /^([1-9]|[1-9][0-9])$/, // 1-99
    PHONE: /^(\+62|62|0)8[1-9][0-9]{6,9}$/
  },
  
  // Command descriptions
  COMMANDS: [
    {command: 'start', description: '<PERSON><PERSON><PERSON><PERSON><PERSON> akun anda ke <PERSON>um'},
    {command: 'help', description: '<PERSON><PERSON><PERSON><PERSON> bantuan dan daftar perintah'},
    {command: 'cekrekom', description: 'Cek rekom yang sedang berjalan'},
    {command: 'cek', description: 'Cek data berdasarkan NIK'},
    {command: 'useronline', description: 'Jumlah user yang online'},
    {command: 'bukarekom', description: '[ADMIN] Buka rekom baru'},
    {command: 'logs', description: '[ADMIN] Tampilkan log sistem'},
    {command: 'cancel', description: 'Batalkan operasi yang sedang berjalan'}
  ],
  
  // Response messages
  MESSAGES: {
    WELCOME: 'Selamat datang di Telegram Bot Simperum!\n\nGunakan /help untuk melihat daftar perintah yang tersedia.',
    HELP: `🤖 **Bantuan Telegram Bot Simperum**\n\n` +
          `**Perintah Umum:**\n` +
          `• /start - Memulai bot\n` +
          `• /help - Tampilkan bantuan ini\n` +
          `• /cekrekom - Cek rekom yang berjalan\n` +
          `• /cek [NIK] - Cek data berdasarkan NIK\n` +
          `• /useronline - Lihat user yang online\n` +
          `• /cancel - Batalkan operasi\n\n` +
          `**Perintah Admin:**\n` +
          `• /bukarekom [nomor] - Buka rekom baru\n` +
          `• /logs - Tampilkan log sistem\n\n` +
          `**Cara Penggunaan:**\n` +
          `Anda dapat menggunakan perintah dengan parameter langsung atau bot akan meminta input yang diperlukan.`,
    
    ADMIN_ONLY: '❌ Perintah ini hanya untuk admin.',
    OPERATION_CANCELLED: '❌ Operasi dibatalkan.',
    UNKNOWN_COMMAND: '❓ Perintah tidak dikenali. Gunakan /help untuk melihat daftar perintah.',
    ERROR_GENERAL: 'Terjadi kesalahan. Silakan coba lagi.',
    
    // Input prompts
    PROMPT_NIK: '📝 Silakan masukkan NIK (16 digit):',
    PROMPT_REKOM: '📝 Masukkan nomor REKOM (1-99):',
    
    // Validation errors
    INVALID_NIK: '❌ NIK harus berupa 16 digit angka. Silakan coba lagi:',
    INVALID_REKOM: '❌ Nomor rekom harus berupa angka 1-99. Silakan coba lagi:',
    
    // Success messages
    REKOM_OPENED: (number) => `✅ REKOM ${number} telah berhasil dibuka.`,
    DATA_UPDATED: (count) => `✅ Berhasil memperbarui ${count} data.`,
    DATA_DELETED: (count) => `✅ Berhasil menghapus ${count} data.`,
    
    // Error messages
    NO_DATA_FOUND: '❌ Data tidak ditemukan.',
    NO_ACTIVE_REKOM: '❌ Tidak ada rekom yang sedang berjalan saat ini.',
    NO_REKOM_TO_UPDATE: '❌ Tidak ada rekom aktif yang dapat diupdate.'
  },
  
  // Database queries
  QUERIES: {
    GET_ACTIVE_REKOM: 'SELECT Tahun, Rekom FROM prm_rekom WHERE IsClosed = 0',
    
    UPDATE_REKOM: 'UPDATE prm_rekom SET Rekom = ? WHERE IsClosed = 0 AND Tahun = YEAR(NOW())',
    
    CHECK_NIK: `
      SELECT 
        p.NoRef, p.Sumber, p.Tahun, p.NIK, p.Nama, p.Kabupaten, p.Kecamatan, p.Kelurahan, 
        p.KodeWilayah, p.ScoreTag, p.MampuSwadaya,
        d.Tahun ProposalTahun, 
        CASE WHEN d.ApprovalDate IS NOT NULL THEN 'Disetujui' ELSE 'Belum Disetujui' END ProposalStatus,
        d.Sumber ProposalSumber,
        b.Tahun BansosTahun, b.Tahapan,
        CASE 
          WHEN d.NIK IS NULL AND d2.NIK IS NOT NULL THEN 'nik_proposal_beda'
          WHEN b.NIK IS NULL AND b2.NIK IS NOT NULL THEN 'nik_bansos_beda'
          WHEN COALESCE(d.NIK,d2.NIK) IS NOT NULL AND COALESCE(b.Tahapan, b2.Tahapan) IS NULL THEN 'need_cleanup' 
          ELSE NULL 
        END Issues 
      FROM prm_pbdt p
        LEFT JOIN prm_proposaldet d ON p.NIK = d.NIK 
        LEFT JOIN prm_bansos b ON p.NIK = b.NIK 
        LEFT JOIN prm_proposaldet d2 ON p.NoRef = d2.NoPBDT 
        LEFT JOIN prm_bansos b2 ON p.NoRef = b2.NoPBDT 
      WHERE p.NIK = AES_ENCRYPT(?, '${ENCKEY}')
    `,
    
    FIX_NIK_PROPOSAL: `
      UPDATE prm_pbdt p
      JOIN prm_proposaldet d ON p.NoRef = d.NoPBDT 
      SET d.NIK = p.NIK
      WHERE p.NIK = AES_ENCRYPT(?, '${ENCKEY}') AND d.NIK <> p.NIK
    `,
    
    CLEANUP_PROPOSAL: `
      DELETE d FROM prm_proposaldet d
        LEFT JOIN prm_bansos b ON d.NIK = b.NIK
      WHERE d.NIK = AES_ENCRYPT(?, '${ENCKEY}') AND d.Tahun = ? AND b.NIK IS NULL
    `
  },
  
  // Issue types and their configurations
  ISSUE_TYPES: {
    'nik_proposal_beda': {
      title: 'NIK Proposal Berbeda',
      description: 'NIK proposal berbeda dengan NIK PBDT',
      action: 'Perbaiki NIK proposal?',
      query: 'FIX_NIK_PROPOSAL'
    },
    'nik_bansos_beda': {
      title: 'NIK Bansos Berbeda', 
      description: 'NIK bansos berbeda dengan NIK PBDT',
      action: 'Perbaiki NIK bansos?',
      query: 'FIX_NIK_BANSOS'
    },
    'need_cleanup': {
      title: 'Perlu Pembersihan',
      description: 'Data masuk proposal namun tidak masuk bansos',
      action: 'Hapus data proposal?',
      query: 'CLEANUP_PROPOSAL'
    },
    'missing_bansos': {
      title: 'Bansos Hilang',
      description: 'Data disetujui namun tidak masuk bansos',
      action: 'Perbaiki data bansos?',
      query: 'ADD_MISSING_BANSOS'
    }
  }
};
