# Telegram Bot Migration Guide

## Overview
This guide helps you migrate from the current telegram.js implementation to the improved version with better security, user experience, and maintainability.

## Key Improvements

### 1. Security Enhancements
- **Environment Variables**: Bot token and admin ID moved to environment variables
- **SQL Injection Prevention**: All queries use parameterized statements
- **Input Validation**: Comprehensive validation for all user inputs
- **Error Handling**: Proper try-catch blocks throughout

### 2. User Experience Improvements
- **Interactive Parameter Collection**: <PERSON><PERSON> asks for missing parameters instead of failing
- **Help System**: Comprehensive /help command with usage instructions
- **Progress Indicators**: Visual feedback during operations
- **Cancellation Support**: Users can cancel operations with /cancel
- **Consistent Messaging**: Standardized message formats with emojis

### 3. Code Quality Improvements
- **Modular Configuration**: Centralized configuration in telegram-config.js
- **Session Management**: Proper session handling with state tracking
- **Clean Architecture**: Separation of concerns and consistent patterns
- **Extensible Design**: Easy to add new commands and issue types

## Migration Steps

### Step 1: Environment Setup
Create a `.env` file in your project root:
```env
TELEGRAM_BOT_TOKEN=your_bot_token_here
TELEGRAM_ADMIN_ID=your_admin_chat_id_here
```

### Step 2: Install Dependencies
Ensure you have the required packages:
```bash
npm install telegraf dotenv
```

### Step 3: Update Your Code
Replace imports in files that use the telegram module:

**Before:**
```javascript
const telegram = require('./common/telegram');
```

**After:**
```javascript
const telegram = require('./common/telegram-improved');
```

### Step 4: Update Bot Initialization
**Before:**
```javascript
// Bot was auto-started in the module
```

**After:**
```javascript
const telegram = require('./common/telegram-improved');
telegram.launch(); // Explicitly start the bot
```

### Step 5: Test the Migration
1. Start your application
2. Test basic commands: /start, /help, /cekrekom
3. Test parameter collection: /cek (without NIK), /bukarekom (without number)
4. Test admin commands (if you're admin)
5. Test cancellation: Start a command, then use /cancel

## Command Comparison

| Old Command | New Command | Improvements |
|-------------|-------------|--------------|
| /start | /start | Better welcome message |
| N/A | /help | New comprehensive help |
| /cekrekom | /cekrekom | Better formatting |
| /cek [NIK] | /cek [NIK] | Interactive NIK input |
| /useronline | /useronline | Better formatting |
| /bukarekom [num] | /bukarekom [num] | Interactive number input |
| /logs | /logs | Truncated output, better formatting |
| N/A | /cancel | New cancellation support |

## New Features

### 1. Interactive Parameter Collection
Commands now ask for missing parameters:
```
User: /cek
Bot: 📝 Silakan masukkan NIK (16 digit):
User: 1234567890123456
Bot: [Shows data for that NIK]
```

### 2. Input Validation
All inputs are validated:
```
User: /cek 123
Bot: ❌ NIK harus berupa 16 digit angka. Silakan coba lagi:
```

### 3. Confirmation Dialogs
Destructive actions require confirmation:
```
Bot: ⚠️ NIK Proposal Berbeda
     NIK proposal berbeda dengan NIK PBDT
     Perbaiki NIK proposal?
     [❌ Batal] [✅ Ya]
```

### 4. Better Error Messages
Clear, user-friendly error messages with emojis and context.

## Configuration Customization

### Adding New Commands
1. Add to `COMMANDS` array in `telegram-config.js`
2. Add command handler in `telegram-improved.js`
3. Implement the handler method

### Adding New Issue Types
1. Add to `ISSUE_TYPES` in `telegram-config.js`
2. Add corresponding SQL query to `QUERIES`
3. The system will automatically handle the issue

### Customizing Messages
Edit the `MESSAGES` object in `telegram-config.js` to customize all bot responses.

## Troubleshooting

### Common Issues

1. **Bot doesn't respond**
   - Check if BOT_TOKEN is correct in environment variables
   - Verify bot is started with `telegram.launch()`

2. **Admin commands don't work**
   - Verify ADMIN_ID matches your Telegram chat ID
   - Get your chat ID by messaging the bot and checking logs

3. **Database errors**
   - Ensure database connection is working
   - Check if all required tables exist

4. **Session issues**
   - Sessions are memory-based and reset on restart
   - For production, consider implementing persistent sessions

### Getting Chat ID
To find your Telegram chat ID:
1. Message your bot
2. Check the console logs for the chat ID
3. Update ADMIN_ID in your environment variables

## Best Practices

1. **Always use environment variables** for sensitive data
2. **Test thoroughly** after migration
3. **Monitor logs** for any errors
4. **Keep the old file as backup** until migration is confirmed working
5. **Update documentation** for your team about new features

## Rollback Plan

If you need to rollback:
1. Restore the original `telegram.js` file
2. Update imports back to the old module
3. Remove environment variable dependencies
4. Restart your application

The new implementation is designed to be backward compatible with your existing usage patterns while providing significant improvements.
