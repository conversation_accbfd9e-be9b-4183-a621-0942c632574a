<template>
  <Modal title="Rekening Bank" :loading="loading" :show.sync="x_show" @onSubmit="Submit">
    <XInput
      type="text"
      label="No Rekening"
      :value.sync="form.NoRekening"
      width="300px"
    />
    <XInput
      type="text"
      label="Nama Rekening"
      :value.sync="form.NamaRekening"
      width="300px"
    />
    <XInput
      type="text"
        label="Cabang Pembuka"
      :value.sync="form.NamaBank"
      width="300px"
    />
  </Modal>
</template>
<script>
export default {
  data: () => ({
    x_show: false,
    form: {},
    loading: false,
  }),
  props: {
    pids: String,
    show: Boolean,
  },
  watch: {
    nobdt() {
      this.Reset()
    },
    show(val) {
      this.x_show = val
      if (val) this.Populate()
    },
    x_show() {
      this.$emit('update:show', this.x_show)
    },
  },
  methods: {
    async Populate() {
      this.loading = true
      this.Reset()
      this.form.ProposalDetIds = this.pids
      let d = await this.$api.call('PRM.SelRekeningBansos', this.form)
      this.loading = false
      if (d.data.length) {
        this.form = { ...d.data[0] }
      }
    },
    Reset() {      
      this.form.NamaBank = ''
      this.form.NamaRekening = ''
      this.form.NoRekening = ''
    },
    async Submit() {
      this.form.ProposalDetIds = this.pids
      let ret = await this.$api.call('PRM.SavRekeningBansos', this.form)
      if (ret.success) {
        this.$emit('success')
        this.$emit('update:show', false)
      }
    },
  },
}
</script>
<style lang="scss"></style>
