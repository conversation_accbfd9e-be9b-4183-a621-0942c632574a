<template>
  <Page title="Review Usulan" :sidebar="true">
    <Sidebar :value.sync="area" :tabs="[2]" />
    <div style="padding: 10px; width: 100%" v-show="area.Kelurahan">
      <DesaGlance
        :dbparams="area"
        :jmlUsulan="jmlUsulan"
        @clickTambahBaru="ClickTambahBaru"
      />
      <div style="padding: 0 5px 7px 5px; display: flex; width: 100%">
        <v-spacer />
        <v-btn
          small
          color="primary"
          @click="OpenProposalDesa"
          style="margin-right: 5px"
        >
          <v-icon left>mdi-file-document-multiple</v-icon>
          PROPOSAL DESA
        </v-btn>
        <v-btn small color="warning" @click="OpenRAB">
          <v-icon left>mdi-shape-plus</v-icon>
          RAB
        </v-btn>
      </div>
      <Grid
        :datagrid.sync="datagrid"
        :dbref="dbref"
        :dbparams="area"
        :disabled="true"
        height="calc(100vh - 230px)"
        :columns="[
          {
            name: '',
            value: 'IsChecked',
            class: 'plain center',
          },
          {
            name: 'NIK',
            value: 'NIK',
            class: 'plain',
          },
          {
            name: '',
            value: 'VerStatsID',
            class: 'plain center',
          },
          {
            name: 'Nama',
            value: 'KRT_Nama',
            width: '200px',
            class: 'fix-width',
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '250px',
            class: 'fix-width',
          },
          {
            name: 'Skor',
            value: 'ScoreTag',
          },
          {
            name: 'DT',
            value: 'NamaData',
          },
          {
            name: '',
            value: 'Dokumen',
            class: 'plain center',
          },
          {
            name: '',
            value: 'Checklist',
            class: 'plain center',
          },
          {
            name: '',
            value: 'HasMessage',
            class: 'plain center',
          },
          {
            name: '',
            value: 'Approval',
            class: 'plain center',
          },
        ]"
      >
        <template v-slot:row-IsChecked="{ row }">
          <Checkbox
            :value.sync="row.CheckedValue"
            checkedIcon="check_box"
            disabledIcon="mdi-lock"
            :disabled="Boolean(row.IsLocked)"
            @click="SubmitProposal(row.NIK, ...arguments)"
          />
        </template>
        <template v-slot:row-NIK="{ row }">
          <v-btn text small color="primary" @click="OpenDetail(row.NoRef)">
            {{ row.NIK || '&lt; kosong &gt;' }}
          </v-btn>
        </template>
        <template v-slot:row-VerStatsID="{ row }">
          <v-icon
            v-if="row.VerStatsID >= 6"
            color="primary"
            v-tooltip="'Sudah Terverifikasi'"
          >
            mdi-account-check
          </v-icon>
          <v-icon v-if="row.VerStatsID < 6" v-tooltip="'Belum Terverifikasi'">
            mdi-account-question-outline
          </v-icon>
        </template>
        <template v-slot:row-Dokumen="{ row }">
          <v-icon
            v-if="row.IsComplete"
            color="success"
            @click="OpenProposal(row.NIK)"
          >
            mdi-file-check
          </v-icon>
          <v-icon v-else @click="OpenProposal(row.NIK)">
            mdi-file-alert-outline
          </v-icon>
        </template>
        <template v-slot:row-Checklist="{ row }">
          <v-icon v-if="row.IsComplete" @click="OpenChecklist(row.NIK)">
            mdi-clipboard-list
          </v-icon>
        </template>
        <template v-slot:row-HasMessage="{ row }">
          <v-icon
            :color="row.HasMessage == 2 ? 'green' : 'silver'"
            v-tooltip="
              row.HasMessage == 2 ? 'Ada pesan baru' : 'tidak ada pesan baru'
            "
            @click="OpenMessages(row.NoRef)"
          >
            {{
              row.HasMessage ? 'mdi-message-text' : 'mdi-message-minus-outline'
            }}
          </v-icon>
        </template>
        <template v-slot:row-Approval="{ row, idx }">
          <v-btn
            v-if="row.CheckedValue"
            text
            small
            color="success"
            @click="ApproveProposal(row, idx)"
          >
            <v-icon left v-if="row.IsApproved">check_box</v-icon>
            <v-icon left v-if="!row.IsApproved"
              >mdi-checkbox-blank-outline</v-icon
            >
            SETUJUI
          </v-btn>
        </template>
      </Grid>
      <ValidasiDetail
        :show.sync="showDetailModal"
        :noRef="selectedRef"
        :area="area"
      />
      <ProposalDetail :nik="selectedNIK" :show.sync="showProposalModal" />
      <ChecklistProposal :nik="selectedNIK" :show.sync="showChecklistModal" />
      <ProposalDesa
        :show.sync="showProposalDesa"
        :kodeDagri="area.KodeDagri"
        :tahun="area.Tahun"
        :disabled="true"
      />
      <RAB
        :show.sync="showRAB"
        :kodeDagri="area.KodeDagri"
        :tahun="area.Tahun"
        :disabled="true"
      />
      <Messages
        :tahun="area.Tahun"
        :noRef="selectedRef"
        :show.sync="showMessages"
      />
    </div>
  </Page>
</template>
<script>
import Sidebar from '../InputUsulan/SideBar.vue'
import DesaGlance from '../DesaGlance.vue'
import ValidasiDetail from '../ValidasiData/ValidasiDetail.vue'
import ProposalDetail from '../InputUsulan/ProposalDetail.vue'
import ChecklistProposal from './Checklist.vue'
import Messages from './Messages.vue'
import RAB from '../InputUsulan/RAB.vue'
import ProposalDesa from '../InputUsulan/ProposalDesa.vue'

export default {
  components: {
    Sidebar,
    DesaGlance,
    ValidasiDetail,
    ProposalDetail,
    ChecklistProposal,
    Messages,
    RAB,
    ProposalDesa,
  },
  data: () => ({
    datagrid: [],
    area: {},
    showDetailModal: false,
    showProposalModal: false,
    showChecklistModal: false,
    showRAB: false,
    showProposalDesa: false,
    showMessages: false,
    selectedRef: null,
    selectedNIK: null,
  }),
  computed: {
    dbref() {
      return 'RLK.ProposalDet'
    },
    jmlUsulan() {
      return this.datagrid.filter((d) => {
        return d.CheckedValue
      }).length
    },
  },
  watch: {
    showDetailModal(val) {
      if (!val) this.selectedRef = null
    },
    '$route.query'() {
      this.area = this.$route.query
    },
  },
  mounted() {
    if (Object.keys(this.$route.query).length) {
      this.area = this.$route.query
    } else if (window.sessionStorage.getItem('side-area')) {
      setTimeout(() => {
        let areaVal = JSON.parse(window.sessionStorage.getItem('side-area'))
        this.$emit('update:value', areaVal)
      }, 500)
    }
  },
  methods: {
    ClickTambahBaru() {
      this.showDetailModal = true
    },
    OpenDetail(noRef) {
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenProposal(nik) {
      this.selectedNIK = nik
      this.showProposalModal = true
    },
    OpenChecklist(nik) {
      this.selectedNIK = nik
      this.showChecklistModal = true
    },
    OpenMessages(noRef) {
      this.selectedRef = noRef
      this.showMessages = true
    },
    OpenRAB() {
      this.showRAB = true
    },
    OpenProposalDesa() {
      this.showProposalDesa = true
    },
    async ApproveProposal(row) {
      let ret = await this.$api.call('RLK.SavProposalApproval', {
        NIK: row.NIK,
        IsApproved: row.IsApproved ? 0 : 1,
      })
      if (ret.success) row.IsApproved = row.IsApproved ? 0 : 1
    },
  },
}
</script>
<style lang="scss">
.page-input-usulan {
  .ui-checkbox {
    .--box.checked {
      color: #1976d2;
    }
  }
}
</style>
