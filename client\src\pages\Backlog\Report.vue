<template>
  <Page title="Laporan" :sidebar="true">
    <div style="background: white">
      <div class="rowgrp">DATABASE</div>
      <div class="rowlnk">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'BLG_RptDashboardRekap',
              groupsheet: '_Group',
            })
          "
          >Rekap Backlog</v-btn
        >
      </div>
      <div class="rowlnk">
        <v-btn
          text
          color="primary"
          @click="
            Generate($event, {
              sp: 'BLG_RptDashboardBNBA',
              groupsheet: '_Group',
            })
          "
          >BNBA PER KABUPATEN</v-btn
        >
      </div>
      <div class="rowlnk">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'BLG_RptSisaPBDT' })"
        >
          BNBA Sisa per Kabupaten
        </v-btn>
      </div>
      <div class="rowlnk">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'BLG_RptRekapPBDT' })"
        >
          Rekap Sisa + Intervensi (Kab/Kota)
        </v-btn>
      </div>
      <div class="rowlnk">
        <v-btn
          text
          color="primary"
          @click="Generate($event, { sp: 'BLG_RptValidasiData' })"
        >
          Validasi Data
        </v-btn>
      </div>
    </div>
    <div>
      <ReportParams
        v-show="showParams"
        :options="reportOptions"
        :reportParams.sync="reportParams"
        :generatedUrl.sync="reportUrl"
        @generate="showReport = true"
      />
      <ReportViewer
        :url="reportUrl"
        :options="reportParams"
        :show.sync="showReport"
      />
    </div>
  </Page>
</template>
<script>
import ReportParams from '../../components/Report/Params.vue'
import ReportViewer from '../../components/ReportViewer.vue'

export default {
  components: {
    ReportParams,
    ReportViewer,
  },
  data: () => ({
    coms: null,
    reportOptions: null,
    reportUrl: '#',
    showReport: false,
    showParams: false,
    reportParams: {},
  }),
  watch: {
    // reportUrl(val) {
    //   if (val) this.showReport = true
    // },
  },
  created() {
    let coms = sessionStorage.getItem('coms-access')
    if (coms) this.coms = JSON.parse(coms)
  },
  methods: {
    Generate(evt, opts) {
      this.showReport = false
      this.showParams = true
      opts.rptname = evt.target.innerText
      this.reportOptions = opts
    },
  },
}
</script>
<style lang="scss">
.rowlnk {
  border-bottom: 1px solid #ddd;
}
.rowgrp {
  border-bottom: 1px solid #ddd;
  font-weight: bold;
  font-size: small;
  padding: 5px;
  background: #f3f3f3;
}
</style>
