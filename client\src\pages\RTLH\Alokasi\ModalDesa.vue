<template>
  <Modal
    :title="params.Kabupaten"
    :show.sync="xshow"
    width="400px"
    @onSubmit="Save"
  >
    <div>
      <div style="padding: 10px 0; display: flex">
        <div>
          Kuota: {{ params.Kuota }}; Teralokasi: {{ alokasi }}; Jml Desa:
          {{ jmlDesa }}
        </div>
        <v-spacer />
        <Checkbox
          :value.sync="hasAllocation"
          v-tooltip="'Yang hanya memiliki kuota'"
          style="margin-right: 10px"
          text=""
        />
      </div>
      <Grid
        dbref="PRM.Alokasi"
        :dbparams="params"
        :datagrid.sync="datagrid"
        :filter="filterGrid"
        :disabled="true"
        :autopaging="false"
        height="calc(80vh - 100px)"
        :columns="[
          {
            name: 'Kecamatan',
            value: 'Kecamatan',
            width: '150px',
          },
          {
            name: '<PERSON><PERSON><PERSON><PERSON>',
            value: '<PERSON><PERSON><PERSON><PERSON>',
            width: '150px',
          },
          {
            name: '<PERSON><PERSON>',
            value: '<PERSON>ota',
            class: 'plain',
          },
        ]"
      >
        <template v-slot:row-Kuota="{ row }">
          <XInput type="number" :value.sync="row.Kuota" width="80px" />
        </template>
      </Grid>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
    // alokasi: 0,
    datagrid: [],
    hasAllocation: false,
  }),
  props: {
    show: Boolean,
    params: Object,
  },
  computed: {
    alokasi() {
      let t = 0
      for (let d of this.datagrid) {
        t += parseInt(d.Kuota) || 0
      }
      return t
    },
    jmlDesa() {
      return this.datagrid.filter((d) => d.Kuota > 0).length
    },
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    async Save() {
      let ret = await this.$api.call('PRM_SavAlokasi', {
        ...this.params,
        XmlAlokasi: this.datagrid,
      })
      if (ret.success) this.$emit('update:show', false)
    },
    filterGrid(row) {
      return this.hasAllocation ? Boolean(row.Kuota) : true
    },
  },
}
</script>
