<template>
  <div class="rekap-angka">
    <v-row no-gutters class="baris">
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>Total RTLH</div>
        <div>{{ rekap.TotalRTLH | format }}</div>
      </v-col>
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>
          Intervensi
          <v-icon
            small
            style="margin-top: -3px"
            v-tooltip="'Sudah mendapatkan bantuan'"
          >
            mdi-help-circle-outline
          </v-icon>
        </div>
        <div>{{ rekap.TotalRTLHLayakHuni | format }}</div>
      </v-col>
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>
          Validasi
          <v-icon
            small
            style="margin-top: -3px"
            v-tooltip="'Double Data / Meninggal'"
          >
            mdi-help-circle-outline
          </v-icon>
        </div>
        <div>{{ rekap.TotalRTLHValidasi | format }}</div>
      </v-col>
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>Sisa</div>
        <div>{{ rekap.TotalRTLHSisa | format }}</div>
        <div class="sub-kotak" style="padding-left: 10px">
          <div>BDT</div>
          <div>
            {{ rekap.TotalRTLHSisaBDT | format }}
          </div>
          <div style="padding-left: 10px">Lengkap</div>
          <div style="padding-left: 10px">
            {{ rekap.TotalRTLHSisaBDT1 | format }}
          </div>
          <div style="padding-left: 10px">Blm Lengkap</div>
          <div style="padding-left: 10px">
            {{ rekap.TotalRTLHSisaBDT0 | format }}
          </div>
        </div>
        <div class="sub-kotak" style="padding-left: 10px">
          <div>Non-BDT</div>
          <div>
            {{ rekap.TotalRTLHSisaNonBDT | format }}
          </div>
          <div style="padding-left: 10px">Lengkap</div>
          <div style="padding-left: 10px">
            {{ rekap.TotalRTLHSisaNonBDT1 | format }}
          </div>
          <div style="padding-left: 10px">Blm Lengkap</div>
          <div style="padding-left: 10px">
            {{ rekap.TotalRTLHSisaNonBDT0 | format }}
          </div>
        </div>
      </v-col>
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>BDT</div>
        <div>{{ rekap.TotalRTLHBDT | format }}</div>
        <div style="padding-left: 10px">Syarat Lengkap</div>
        <div style="padding-left: 10px">{{ rekap.TotalRTLHBDT1 | format }}</div>
        <div style="padding-left: 10px">Belum Lengkap</div>
        <div style="padding-left: 10px">{{ rekap.TotalRTLHBDT0 | format }}</div>
      </v-col>
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>Non-BDT</div>
        <div>{{ rekap.TotalRTLHNonBDT | format }}</div>
        <div style="padding-left: 10px">Syarat Lengkap</div>
        <div style="padding-left: 10px">
          {{ rekap.TotalRTLHNonBDT1 | format }}
        </div>
        <div style="padding-left: 10px">Belum Lengkap</div>
        <div style="padding-left: 10px">
          {{ rekap.TotalRTLHNonBDT0 | format }}
        </div>
      </v-col>
    </v-row>
    <v-row no-gutters class="baris">
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>Total Backlog</div>
        <div>{{ rekap.TotalBacklog | format }}</div>
      </v-col>
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>
          Intervensi
          <v-icon
            small
            style="margin-top: -3px"
            v-tooltip="'Sudah mendapatkan bantuan'"
          >
            mdi-help-circle-outline
          </v-icon>
        </div>
        <div>{{ rekap.TotalBacklogLayak | format }}</div>
      </v-col>
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>
          Validasi
          <v-icon
            small
            style="margin-top: -3px"
            v-tooltip="'Double Data / Meninggal'"
          >
            mdi-help-circle-outline
          </v-icon>
        </div>
        <div>{{ rekap.TotalBacklogValidasi | format }}</div>
      </v-col>
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>Sisa</div>
        <div>{{ rekap.TotalBacklogSisa | format }}</div>
        <div class="sub-kotak">
          <div style="padding-left: 10px">BDT</div>
          <div style="padding-left: 10px">
            {{ rekap.TotalBacklogSisaBDT | format }}
          </div>
          <div style="padding-left: 20px">Lengkap</div>
          <div style="padding-left: 20px">
            {{ rekap.TotalBacklogSisaBDT1 | format }}
          </div>
          <div style="padding-left: 20px">Blm Lengkap</div>
          <div style="padding-left: 20px">
            {{ rekap.TotalBacklogSisaBDT0 | format }}
          </div>
        </div>
        <div class="sub-kotak">
          <div style="padding-left: 10px">Non-BDT</div>
          <div style="padding-left: 10px">
            {{ rekap.TotalBacklogSisaNonBDT | format }}
          </div>
          <div style="padding-left: 20px">Lengkap</div>
          <div style="padding-left: 20px">
            {{ rekap.TotalBacklogSisaNonBDT1 | format }}
          </div>
          <div style="padding-left: 20px">Blm Lengkap</div>
          <div style="padding-left: 20px">
            {{ rekap.TotalBacklogSisaNonBDT0 | format }}
          </div>
        </div>
      </v-col>
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>BDT</div>
        <div>
          {{ (rekap.TotalBacklogBDT1 + rekap.TotalBacklogBDT0) | format }}
        </div>
        <div style="padding-left: 10px">Syarat Lengkap</div>
        <div style="padding-left: 10px">
          {{ rekap.TotalBacklogBDT1 | format }}
        </div>
        <div style="padding-left: 10px">Belum Lengkap</div>
        <div style="padding-left: 10px">
          {{ rekap.TotalBacklogBDT0 | format }}
        </div>
      </v-col>
      <v-col cols="12" md="6" lg="2" class="kotak">
        <div>Non-BDT</div>
        <div>
          {{ (rekap.TotalBacklogNonBDT0 + rekap.TotalBacklogNonBDT1) | format }}
        </div>
        <div style="padding-left: 10px">Syarat Lengkap</div>
        <div style="padding-left: 10px">
          {{ rekap.TotalBacklogNonBDT1 | format }}
        </div>
        <div style="padding-left: 10px">Belum Lengkap</div>
        <div style="padding-left: 10px">
          {{ rekap.TotalBacklogNonBDT0 | format }}
        </div>
      </v-col>
    </v-row>
    <v-row no-gutters class="baris">
      <v-col cols="12" md="4" lg="4" class="kotak">
        <div>Data Kemiskinan</div>
        <div>{{ rekap.TotalMiskin | format }}</div>
      </v-col>
      <v-col cols="12" md="4" lg="4" class="kotak">
        <div>
          Validasi
          <v-icon
            small
            style="margin-top: -3px"
            v-tooltip="'Double Data / Meninggal'"
          >
            mdi-help-circle-outline
          </v-icon>
        </div>
        <div>{{ rekap.TotalMiskinValidasi | format }}</div>
      </v-col>
      <v-col cols="12" md="4" lg="4" class="kotak">
        <div>Sisa</div>
        <div>{{ rekap.TotalMiskinSisa | format }}</div>
      </v-col>
    </v-row>
  </div>
</template>
<script>
export default {
  data: () => ({
    rekap: {},
  }),
  async mounted() {
    let res = await this.$api.call('EVO_RptRekapAngka')
    this.rekap = res.data[0]
  },
}
</script>
<style lang="scss">
.rekap-angka {
  // max-width: 300vw;
  .baris {
    // display: flex;
    padding: 15px;
    background: rgba(255, 255, 255, 0.5);
    // margin-bottom: 10px;
    // justify-content: space-between;

    .kotak {
      background: rgba(255, 255, 255, 0.7);
      border-radius: 5px;
      padding: 15px;
      // width: calc(17% - 20px);
      box-shadow: 0 0 3px rgba(0, 0, 0, 0.1);

      & > div:nth-child(odd) {
        font-size: 14px;
        font-weight: bold;
      }

      & .sub-kotak {
        display: inline-block;
        vertical-align: top;
        font-size: small !important;
        font-weight: normal !important;
        margin-right: 10px;
        & > div:nth-child(odd) {
          font-size: small;
          font-weight: bold;
        }
      }
    }
  }
}
</style>
