import os
import sys
import mysql.connector
from mysql.connector import pooling
from dotenv import load_dotenv
import pathlib

# Load environment variables
env_path = pathlib.Path(__file__).parent.parent.parent / '.env'
load_dotenv(dotenv_path=env_path)
print(env_path)

# # MySQL Configuration with increased timeouts
# config = {
#     'user': os.getenv('DB_USER'),
#     'password': os.getenv('DB_PASSWORD'),
#     'host': os.getenv('DB_HOST'),
#     'database': os.getenv('DB_NAME'),
#     'port': int(os.getenv('DB_PORT', '3307')),
#     'connection_timeout': 300,  # 5 minutes
#     'autocommit': True,
#     'connect_timeout': 12000,  # 5 minutes
#     'sql_mode': 'TRADITIONAL',
#     'charset': 'utf8mb4',
#     'use_unicode': True,
#     'get_warnings': True
# }

# # Create connection pool
# try:
#     pool = pooling.MySQLConnectionPool(
#         pool_name="batch_pool",
#         pool_size=int(os.getenv('DB_CONNECTION_LIMIT', '2')),
#         pool_reset_session=True,
#         **config
#     )
#     print("Database connection pool created successfully")
# except Exception as e:
#     print(f"Error creating connection pool: {e}")
#     sys.exit(1)

def connect():
    mydb = mysql.connector.connect(
        host=os.getenv('DB_HOST'),
        user=os.getenv('DB_USER'),
        password=os.getenv('DB_PASSWORD'),
        database=os.getenv('DB_NAME'),
        port=int(os.getenv('DB_PORT', '3307')),
    )
    return mydb

def run(row):
    """Process a single record"""
    kabupaten = row[0]  # AreaName
    print(kabupaten)
    
    # Update query
    # update_query = """
    #     UPDATE tmp_dtse td 
    #     JOIN prm_pbdt pp ON td.NIKENC = pp.NIK 
    #     SET td.IDBDT = pp.Noref 
    #     WHERE td.nmkab = %s
    # """
    update_query = """
        insert ignore into tmp_1022final
        select nik, AES_ENCRYPT(nik , '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5') NIKENC, td.nama, td.alamat, -2, 'rtlh', td.Intervensi, td.nmkab, td.nmkec, td.nmdesa, td.desil_dtsen from tmp_dtse td 
        where td.Intervensi IS NULL AND td.nmkab = %s
        LIMIT %s
    """
    
    connection = None
    try:
        # connection = pool.get_connection()
        connection = connect()
        cursor = connection.cursor()
        print(" - stating")
        cursor.execute(update_query, (kabupaten,))
        print(" - finished")
        connection.commit()
        cursor.close()
        return None
    except Exception as e:
        print(f"Error processing {kabupaten}: {e}")
        return e
    finally:
        if connection and connection.is_connected():
            connection.close()

def main():
    """Main function"""
    try:
        # Get connection from pool
        # connection = pool.get_connection()
        connection = connect()
        cursor = connection.cursor()
        
        # Query for Kabupaten records
        select_query = """
            SELECT aa.AreaName Kabupaten 
            FROM arch_area aa 
            WHERE aa.ParentAreaID = 33 
            AND aa.AreaName NOT IN (
                'CILACAP','BANYUMAS','PURBALINGGA','BANJARNEGARA',
                'KEBUMEN','PURWOREJO','WONOSOBO','MAGELANG','SUKOHARJO',
                'BOYOLALI','WONOGIRI','KLATEN','KARANGANYAR','SRAGEN','GROBOGAN',
                'BLORA','REMBANG','PATI','KUDUS','JEPARA','DEMAK',
                'SEMARANG','TEMANGGUNG'
            )
        """
        
        cursor.execute(select_query)
        records = cursor.fetchall()
        cursor.close()
        connection.close()
        
        # Process each record
        for row in records:
            result = run(row)
            if result:
                raise result
                
        print('processed successfully')
        sys.exit(0)
        
    except Exception as e:
        print(f'Error: {e}')
        sys.exit(1)

if __name__ == "__main__":
    main()