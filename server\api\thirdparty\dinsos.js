const axios = require("axios");
const querystring = require('querystring');
var db = require("../../common/db");

const creds = `username=disperakim&token=eIJCVbmZfYdusKiqqVDN`
const url = `https://caribdt.dinsos.jatengprov.go.id/api`

const checkPkp = async (nik) => {
  process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
  // console.log(row.NIK)
  let ret;
  let retries = 3;
  while (retries > 0) {
    try {
      ret = await axios.post('https://my.pkp.go.id/cekbantuan', querystring.stringify({
        j:1,
        nik:nik
      }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          cookie: 'PHPSESSID=cu5rrqqarnkrrrpflq1olr7q77'
        }
      });
      break; // Success, exit the retry loop
    } catch (error) {
      retries--;
      if (retries === 0 || (error.code !== 'ECONNRESET' && !error.message.includes('ECONNRESET'))) {
        // If no retries left or not a connection reset error, re-throw
        throw error;
      }
      console.log(`Retrying request for NIK: ${nik}. Retries left: ${retries}`);
      // Optional: Add a small delay before retrying
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  // console.log(ret.data)
  
  // Extract columns 4 and 5 from the HTML table
  const ext = extractColumns4And5(ret.data);
  console.log('pkp '+nik+':', ext)
  
  return ext.column4 == 'Eligible' || ext.column5 == 'Eligible' ;
}

// Function to extract columns 4 and 5 from HTML table string
function extractColumns4And5(htmlString) {
  // Regular expression to match td elements
  const tdRegex = /<td[^>]*>(.*?)<\/td>/g;
  const matches = [];
  let match;
  
  // Extract all td elements
  while ((match = tdRegex.exec(htmlString)) !== null) {
    matches.push(match[1]);
  }
  
  let col5 = (matches[6] || '')
  if (col5.match('sudah pernah menerima')) {
    col5 = col5.split('<br>')[0].replace('sudah pernah menerima bantuan ','').replace('tahun ','')
  } else {
    col5 = ''
  }
  // Return columns 4 and 5 (index 3 and 4)
  return {
    column4: matches[5] || '',
    column5: col5
  };
}


module.exports = {
  async getNIKOld(nik) {
    let d = await axios
      .get(`${url}/nik_perakim9501/?${creds}&nik=${nik}`)
      .catch((err) => {
        console.log(`get_nikold_perakim:`+err.response?.statusText);
      });
    return d ? d.data : {};
  },
  async getByAreaOld(kodeDagri) {
    kodeDagri = kodeDagri+''
    let kdkab = kodeDagri.substr(2, 2)
    let kdkec = kodeDagri.substr(4, 2)
    let kddesa = kodeDagri.substr(-4)
    let d = await axios
      .get(`${url}/desa_perakim9502/?${creds}&kdkab=${kdkab}&kdkec=${kdkec}&kddesa=${kddesa}`)
      .catch((err) => {
        console.log(`get_byarea_perakim:`+err.response?.statusText);
      });
    return d.data
  },
  async getNIK(nik) {
    console.log('getNIK: ' + nik)
    let params = new URLSearchParams();
    params.append('username', 'perakim');
    params.append('password', 'apiperakim');
    params.append('nik', nik);

    let d = await axios
      .request({
        url: `https://dtjateng.dinsos.jatengprov.go.id/api/perakim/cek-data-nik`,
        method: 'POST',
        headers: {'Content-Type': 'application/x-www-form-urlencoded'},
        data: params.toString()
      })
      .catch((err) => {
        console.log(`get_nik_perakim: `+err);
      });
    let rd = d ? d.data : {}
    console.log('d: ' + JSON.stringify(rd))
    rd.alamat = rd.alamat || ""
    // if not exist, check pkp
    if (!d.NIK && !d.nik) {
      let pkp = await checkPkp(nik)
      if (pkp) {
        rd = {NIK: nik, IDBDT: '444'+nik}
      }
    }
    return rd;
  },
  async getByArea(kodeDagri) {
    kodeDagri = kodeDagri+''
    let kdkab = kodeDagri.substr(2, 2)
    let kdkec = kodeDagri.substr(4, 2)
    let kddesa = kodeDagri.substr(-4)

    let params = new URLSearchParams();
    params.append('username', 'perakim');
    params.append('password', 'apiperakim');
    params.append('kdkab', kdkab);
    params.append('kdkec', kdkec);
    params.append('kddesa', kddesa);

    let d = await axios
      .post(`https://caribdt.dinsos.jatengprov.go.id/api/perakim/cek-data-desa`, params.toString(), {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      })
      .catch((err) => {
        console.log(`get_byarea_perakim:`+err.response?.statusText);
      });
    return d ? d.data : {}
  },
};
