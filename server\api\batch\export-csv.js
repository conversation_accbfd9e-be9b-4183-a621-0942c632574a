require('dotenv').config({path: require('path').resolve(__dirname, '../../.env')});
const fs = require('fs');
const {stringify} = require('csv-stringify/sync');
const mysql = require('mysql');
const util = require('util');

// MySQL Configuration
const pool = mysql.createPool({
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '2'),
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '3307'),
  waitForConnections: true,
  queueLimit: 0
});

pool.getConnection((err, connection) => {
  if (err) {
    if (err.code === "PROTOCOL_CONNECTION_LOST") {
      console.error("Database connection was closed.");
    }
    if (err.code === "ER_CON_COUNT_ERROR") {
      console.error("Database has too many connections.");
    }
    if (err.code === "ECONNREFUSED") {
      console.error("Database connection was refused.");
    }
  }
  if (connection) connection.release();
  return;
});

pool.query = util.promisify(pool.query);

// Function to export data to CSV
async function run(row) {
  try {
    // Dummy SQL query - replace with your actual query
    const query = `
      select tf.NIK, tf.Nama, tf.Alamat, tf.Kabupaten, tf.Kecamatan, tf.Kelurahan, tf.Intervensi, tf.desil, tf.StatusPKP from tmp_1022final tf 
        where tf.desil is not null AND tf.Kabupaten = '${row.Kabupaten}'
        order by tf.Kabupaten, tf.Kecamatan, tf.Kelurahan
    `;
    
    console.log('Executing query...');
    const results = await pool.query(query);
    
    if (results.length === 0) {
      console.log('No data found to export');
      return;
    }
    
    // Convert to CSV format
    const csvData = stringify(results, {
      header: true,
      quoted: true
    });
    
    // Save to file in tmp directory
    const fileName = `export_${row.Kabupaten}.csv`;
    const filePath = require('path').resolve(__dirname, 'tmp', fileName);
    
    fs.writeFileSync(filePath, csvData);
    console.log(`Data exported successfully to ${filePath}`);
    
  } catch (error) {
    console.error('Error exporting data:', error);
  }
}

// Run the export function
async function main() {
  try {

    // await readCSV()

    // const records = await executeQuery(`select DISTINCT t.KodeDagri from tmp_1022 t `);
    const records = await pool.query(`select DISTINCT t.Kabupaten from tmp_1022final t`);

    // const records = [{ Kabupaten: 'KLATEN' }]
    // console.log(records)
    for (const row of records) {
      console.log(row.Kabupaten)
      await run(row)
    }

    console.log('processed successfully');

    // End MySQL pool
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(0);
    });

    process.exit(0)
  } catch (error) {
    console.error('Error:', error);

    // End MySQL pool on error
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(1);
    });
  }
}

main();