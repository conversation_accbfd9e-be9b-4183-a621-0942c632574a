<template>
  <div class="grid-mobile-det" v-if="show">
    <div class="--header">
      <div>Detail</div>
      <v-spacer />
      <v-icon @click.native="Close">mdi-close</v-icon>
    </div>
    <div class="--body">
      <div style="margin-top: -15px" v-if="!isEdit"></div>
      <div v-for="(col, colidx) in columns" :key="colidx">
        <div class="col-title" v-if="!isEdit">{{ col.name }} :</div>
        <slot
          :name="'row-' + col.value"
          :row="row"
          :idx="idx"
          :isMobile="true"
          v-if="!col.hide && !isEdit"
        >
          <div
            v-if="col.editable && col.editable.com.name == 'Checkbox'"
            style="text-align: center"
          >
            <v-icon>
              {{
                row[col.value]
                  ? 'mdi-checkbox-marked-outline'
                  : 'mdi-crop-square'
              }}
            </v-icon>
          </div>
          <div
            v-if="
              col.editable &&
              col.editable.com == 'Input' &&
              col.editable.type == 'password'
            "
          >
            * * * * *
          </div>
          <div
            v-else
            :class="col.class"
            :style="{
              width: col.width,
            }"
          >
            {{ row[col.value] }}
          </div>
        </slot>
        <div v-if="isEdit">
          <component
            v-if="col.editable"
            :label="col.name"
            :is="col.editable.com"
            v-bind="col.editable"
            :dbparams="
              typeof col.editable.dbparams == 'function'
                ? col.editable.dbparams(editedData)
                : col.editable.dbparams
            "
            :value.sync="editedData[col.editable.value || col.value]"
            :text.sync="editedData[col.editable.text]"
          ></component>
          <XInput
            v-else
            type="text"
            :label="col.name"
            :value="editedData[col.value]"
            :disabled="true"
          />
        </div>
      </div>
    </div>
    <div class="--footer" v-if="!disabled">
      <v-btn text color="error" v-if="canDelete">HAPUS</v-btn>
      <v-spacer />
      <v-btn
        text
        color="primary"
        v-if="canDelete && !isEdit"
        @click="isEdit = true"
      >
        EDIT
      </v-btn>
      <v-btn text color="primary" v-if="canDelete && isEdit" @click="Save">
        SIMPAN
      </v-btn>
    </div>
  </div>
</template>
<script>
export default {
  data: () => ({
    isEdit: false,
  }),
  props: {
    show: Boolean,
    editedData: Object,
    idx: Number,
    row: Object,
    columns: Array,
    disabled: Boolean,
    editMode: {
      type: Array,
      default: () => [],
    },
  },
  watch: {
    show(val) {
      this.isEdit = false
      if (val && !this.disabled) this.isEdit = true
    },
  },
  computed: {
    canEdit() {
      return this.editMode.length == 0 || this.editMode.includes('edit')
    },
    canDelete() {
      return (
        (this.editMode.length == 0 || this.editMode.includes('delete')) &&
        !this.disabled
      )
    },
  },
  methods: {
    Close() {
      this.$emit('update:show', false)
    },
    Save() {
      this.$emit('save', this.editedData)
    },
  },
}
</script>
<style lang="scss">
.grid-mobile-det {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: white;
  z-index: 10;
  .--header {
    padding: 10px;
    display: flex;
  }
  .--body {
    padding: 15px;
    height: calc(100vh - 100px);
    border-top: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
  }
  .--footer {
    padding: 10px;
    display: flex;
  }
  .col-title {
    font-size: small;
    font-weight: bold;
    margin-top: 15px;
  }
}
</style>
