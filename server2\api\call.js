const db = require('../common/db')
const events = require('./events');
const { authHook } = require('./auth')

// Fastify plugin for the call routes
async function callRoutes(fastify, options) {
  // Register the auth hook as a preHandler hook for this plugin
  fastify.addHook('preHandler', authHook);

  // POST route for /call/:sp
  fastify.post('/call/:sp', async function (request, reply) {
    try {
      let d = null
      if (request.params.sp.match(/_$/)) {
        let validation = await db.exec(request.params.sp + '1', request.body)
        if (validation.length && validation[0].ErrCode) {
          return reply.send({
            success: false,
            message: validation && validation.length && validation[0].Message ? validation[0].Message : '',
            type: 'error',
          })
        }
        d = await db.exec(request.params.sp + '2', request.body)
      } else {
        d = await db.exec(request.params.sp, request.body)
      }

      let data = d
      if (request.body?.['__grid']) {
        data = { data: d }
      }

      if (data && data.length && data[0].Notification)
        events.notify(JSON.parse(data[0].Notification))

      if (data && data.length && data[0].ErrCode) {
        if (data && data.length && data[0].Error) {
          if (data[0].Error.match(/ER_/)) {
            if (!data[0].Error.match(/ER_BAD_NULL_ERROR/) &&
              !data[0].Error.match(/ER_DUP_ENTRY/)) {
              // Access telegram through fastify instance
              if (fastify.telegram) {
                fastify.telegram.sendAdmin(request.body._userId + ':' + data[0].Error)
              }
            }
          }
        }
        return reply.send({
          success: false,
          data: data,
          type: 'error',
          message: data && data.length && data[0].Message ? data[0].Message : '',
        })
      } else {
        return reply.send({
          success: true,
          data: data,
          type: 'array',
          message: data && data.length && data[0].Message ? data[0].Message : '',
        })
      }
    } catch (ex) {
      console.log(request.params.sp, ex)
      // Access telegram through fastify instance
      if (fastify.telegram) {
        fastify.telegram.sendAdmin('Error /call (' + request.params.sp + '): ' + ex.message)
      }
      return reply.send({
        success: false,
        data: 'Error',
        type: 'error',
        message: 'Error ' + ex.message,
      })
    }
  })
}

module.exports = callRoutes
