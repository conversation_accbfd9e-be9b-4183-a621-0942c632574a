const {Telegraf, Markup, session} = require('telegraf');
const fs = require('fs');
const db = require('./db');
const events = require('../api/events');
const config = require('./telegram-config');

class TelegramBot {
  constructor() {
    this.bot = new Telegraf(config.BOT_TOKEN);
    this.setupMiddleware();
    this.setupCommands();
    this.setupActions();
    this.setupTextHandler();
  }

  setupMiddleware() {
    // Session middleware for persistent state
    this.bot.use(session({
      defaultSession: () => ({
        state: 'idle',
        data: {},
        awaitingInput: null
      })
    }));

    // Admin check middleware
    this.bot.use((ctx, next) => {
      ctx.isAdmin = () => ctx.chat.id.toString() === config.ADMIN_ID;
      return next();
    });

    // Error handling middleware
    this.bot.catch((err, ctx) => {
      console.error('Bot error:', err);
      ctx.reply('<PERSON><PERSON><PERSON><PERSON> k<PERSON>. <PERSON>lakan coba lagi.');
    });
  }

  setupCommands() {
    // Set bot commands
    this.bot.telegram.setMyCommands(config.COMMANDS);

    // Command handlers
    this.bot.command('start', this.handleStart.bind(this));
    this.bot.command('help', this.handleHelp.bind(this));
    this.bot.command('cekrekom', this.handleCekRekom.bind(this));
    this.bot.command('cek', this.handleCek.bind(this));
    this.bot.command('useronline', this.handleUserOnline.bind(this));
    this.bot.command('bukarekom', this.handleBukaRekom.bind(this));
    this.bot.command('logs', this.handleLogs.bind(this));
    this.bot.command('cancel', this.handleCancel.bind(this));
  }

  setupActions() {
    this.bot.action('cancel', this.handleCancel.bind(this));
    this.bot.action(/^confirm_(.+)$/, this.handleConfirmAction.bind(this));
  }

  setupTextHandler() {
    this.bot.on('text', this.handleText.bind(this));
  }

  // Command Handlers
  async handleStart(ctx) {
    try {
      ctx.session.state = 'idle';
      await ctx.reply(config.MESSAGES.WELCOME);
    } catch (error) {
      console.error('Error in handleStart:', error);
      await ctx.reply(config.MESSAGES.ERROR_GENERAL);
    }
  }

  async handleHelp(ctx) {
    try {
      await ctx.reply(config.MESSAGES.HELP, {parse_mode: 'Markdown'});
    } catch (error) {
      console.error('Error in handleHelp:', error);
      await ctx.reply(config.MESSAGES.ERROR_GENERAL);
    }
  }

  async handleCekRekom(ctx) {
    try {
      const res = await db.query('SELECT Tahun, Rekom FROM prm_rekom WHERE IsClosed = 0');
      
      if (res.length > 0) {
        await ctx.reply(`📋 Rekom yang sedang berjalan:\n\n🗓️ Tahun: ${res[0].Tahun}\n📝 Rekom: ${res[0].Rekom}`);
      } else {
        await ctx.reply('❌ Tidak ada rekom yang sedang berjalan saat ini.');
      }
    } catch (error) {
      console.error('Error in handleCekRekom:', error);
      await ctx.reply('Terjadi kesalahan saat mengambil data rekom.');
    }
  }

  async handleCek(ctx) {
    try {
      const message = ctx.message.text;
      const nikMatch = message.match(/\/cek\s+(\d{16})/);
      
      if (nikMatch) {
        const nik = nikMatch[1];
        await this.processNikCheck(ctx, nik);
      } else {
        ctx.session.state = 'awaiting_nik';
        ctx.session.awaitingInput = 'nik_check';
        await ctx.reply('📝 Silakan masukkan NIK (16 digit):');
      }
    } catch (error) {
      console.error('Error in handleCek:', error);
      await ctx.reply('Terjadi kesalahan saat memproses perintah cek.');
    }
  }

  async handleUserOnline(ctx) {
    try {
      const clients = Object.keys(events.clients);
      const count = clients.length;
      
      let reply = `👥 Jumlah user yang terkoneksi: ${count}\n\n`;
      if (count > 0) {
        reply += '📋 Daftar user:\n' + clients.map(client => `• ${client}`).join('\n');
      }
      
      await ctx.reply(reply);
    } catch (error) {
      console.error('Error in handleUserOnline:', error);
      await ctx.reply('Terjadi kesalahan saat mengambil data user online.');
    }
  }

  async handleBukaRekom(ctx) {
    try {
      if (!ctx.isAdmin()) {
        await ctx.reply('❌ Perintah ini hanya untuk admin.');
        return;
      }

      const message = ctx.message.text;
      const rekomMatch = message.match(/\/bukarekom\s+(\d{1,2})/);
      
      if (rekomMatch) {
        const rekomNumber = rekomMatch[1];
        await this.processBukaRekom(ctx, rekomNumber);
      } else {
        ctx.session.state = 'awaiting_rekom';
        ctx.session.awaitingInput = 'buka_rekom';
        await ctx.reply('📝 Masukkan nomor REKOM (1-99):');
      }
    } catch (error) {
      console.error('Error in handleBukaRekom:', error);
      await ctx.reply('Terjadi kesalahan saat memproses perintah buka rekom.');
    }
  }

  async handleLogs(ctx) {
    try {
      if (!ctx.isAdmin()) {
        await ctx.reply('❌ Perintah ini hanya untuk admin.');
        return;
      }

      const logs = fs.readFileSync('/app/tmp/backend.log', 'utf-8');
      const truncatedLogs = logs.length > 4000 ? logs.slice(-4000) + '\n...(truncated)' : logs;
      
      await ctx.reply(`📋 System Logs:\n\`\`\`\n${truncatedLogs}\n\`\`\``, {parse_mode: 'Markdown'});
    } catch (error) {
      console.error('Error in handleLogs:', error);
      await ctx.reply('Terjadi kesalahan saat membaca log sistem.');
    }
  }

  async handleCancel(ctx) {
    try {
      ctx.session.state = 'idle';
      ctx.session.awaitingInput = null;
      ctx.session.data = {};
      
      if (ctx.callbackQuery) {
        await ctx.editMessageReplyMarkup();
      }
      
      await ctx.reply('❌ Operasi dibatalkan.');
    } catch (error) {
      console.error('Error in handleCancel:', error);
    }
  }

  // Text Handler for awaiting inputs
  async handleText(ctx) {
    try {
      if (ctx.session.state === 'idle') {
        await ctx.reply('❓ Perintah tidak dikenali. Gunakan /help untuk melihat daftar perintah.');
        return;
      }

      const input = ctx.message.text.trim();

      switch (ctx.session.awaitingInput) {
      case 'nik_check':
        if (this.validateNik(input)) {
          await this.processNikCheck(ctx, input);
        } else {
          await ctx.reply('❌ NIK harus berupa 16 digit angka. Silakan coba lagi:');
        }
        break;

      case 'buka_rekom':
        if (this.validateRekomNumber(input)) {
          await this.processBukaRekom(ctx, input);
        } else {
          await ctx.reply('❌ Nomor rekom harus berupa angka 1-99. Silakan coba lagi:');
        }
        break;

      default:
        await ctx.reply('❓ Input tidak dikenali. Gunakan /cancel untuk membatalkan.');
      }
    } catch (error) {
      console.error('Error in handleText:', error);
      await ctx.reply('Terjadi kesalahan saat memproses input.');
    }
  }

  // Validation methods
  validateNik(nik) {
    return config.PATTERNS.NIK.test(nik);
  }

  validateRekomNumber(number) {
    return config.PATTERNS.REKOM_NUMBER.test(number);
  }

  // Process methods
  async processNikCheck(ctx, nik) {
    try {
      ctx.session.state = 'idle';
      ctx.session.awaitingInput = null;
      
      await ctx.sendChatAction('typing');
      
      console.log(nik)
      const res = await db.query(config.QUERIES.CHECK_NIK, [nik]);
      
      if (res.length > 0) {
        console.log(res)
        const data = res[0];
        ctx.session.data = {nik, tahun: data.ProposalTahun};
        
        const message = this.formatNikCheckResult(data);
        await ctx.reply(message);
        
        // Handle admin actions for issues
        if (ctx.isAdmin() && data.Issues) {
          await this.handleDataIssues(ctx, data);
        }
      } else {
        await ctx.reply('❌ Data dengan NIK tersebut tidak ditemukan.');
      }
    } catch (error) {
      console.error('Error in processNikCheck:', error);
      await ctx.reply('Terjadi kesalahan saat mencari data NIK.');
    }
  }

  formatNikCheckResult(data) {
    return `👤 **Data Ditemukan**\n\n` +
           `📝 Nama: ${data.Nama}\n` +
           `📍 Lokasi: ${data.Kabupaten}, ${data.Kecamatan}, ${data.Kelurahan}\n` +
           `📊 Skor: ${data.ScoreTag || '-'} | Swadaya: ${data.MampuSwadaya || '-'}\n` +
           `📋 Proposal: ${data.ProposalTahun || '-'} [${data.ProposalSumber || '-'}] ${data.ProposalStatus || '-'}\n` +
           `💰 Bansos: ${data.BansosTahun || '-'} ${data.Tahapan || '-'}` +
           `${data.Issues ? `\n\n⚠️ **MASALAH**: ${data.Issues}` : ''}`;
  }

  async processBukaRekom(ctx, rekomNumber) {
    try {
      ctx.session.state = 'idle';
      ctx.session.awaitingInput = null;
      
      const result = await db.query(config.QUERIES.UPDATE_REKOM, [`REKOM ${rekomNumber}`]);
      
      if (result.affectedRows > 0) {
        await ctx.reply(`✅ REKOM ${rekomNumber} telah berhasil dibuka.`);
      } else {
        await ctx.reply('❌ Tidak ada rekom aktif yang dapat diupdate.');
      }
    } catch (error) {
      console.error('Error in processBukaRekom:', error);
      await ctx.reply('Terjadi kesalahan saat membuka rekom.');
    }
  }

  async handleDataIssues(ctx, data) {
    try {
      if (!data.Issues || !config.ISSUE_TYPES[data.Issues]) return;

      const issueConfig = config.ISSUE_TYPES[data.Issues];
      const keyboard = Markup.inlineKeyboard([
        Markup.button.callback('❌ Batal', 'cancel'),
        Markup.button.callback('✅ Ya', `confirm_${data.Issues}`)
      ]);

      await ctx.reply(
        `⚠️ **${issueConfig.title}**\n\n${issueConfig.description}\n\n${issueConfig.action}`,
        {parse_mode: 'Markdown', ...keyboard}
      );
    } catch (error) {
      console.error('Error in handleDataIssues:', error);
      await ctx.reply(config.MESSAGES.ERROR_GENERAL);
    }
  }

  async handleConfirmAction(ctx) {
    try {
      const action = ctx.match[1]; // Extract action from callback data
      const sessionData = ctx.session.data;

      if (!sessionData.nik) {
        await ctx.reply('❌ Data sesi tidak valid. Silakan coba lagi.');
        return;
      }

      await ctx.editMessageReplyMarkup();

      const issueConfig = config.ISSUE_TYPES[action];
      if (!issueConfig || !config.QUERIES[issueConfig.query]) {
        await ctx.reply('❌ Aksi tidak valid.');
        return;
      }

      const query = config.QUERIES[issueConfig.query];
      const params = action === 'cleanup_proposal'
        ? [sessionData.nik, sessionData.tahun]
        : [sessionData.nik];

      const result = await db.query(query, params);

      if (result.affectedRows > 0) {
        await ctx.reply(config.MESSAGES.DATA_UPDATED(result.affectedRows));
      } else {
        await ctx.reply(config.MESSAGES.NO_DATA_FOUND);
      }
    } catch (error) {
      console.error('Error in handleConfirmAction:', error);
      await ctx.reply(config.MESSAGES.ERROR_GENERAL);
    }
  }

  // Public methods
  launch() {
    this.bot.launch();
    console.log('🤖 Telegram Bot Active');
    
    // Graceful shutdown
    process.once('SIGINT', () => this.bot.stop('SIGINT'));
    process.once('SIGTERM', () => this.bot.stop('SIGTERM'));
  }

  async sendMessage(chatId, text) {
    try {
      if (text) {
        await this.bot.telegram.sendMessage(chatId, text);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  }

  async sendAdminMessage(text) {
    await this.sendMessage(config.ADMIN_ID, text);
  }
}

// Create and export bot instance
const telegramBot = new TelegramBot();

module.exports = {
  bot: telegramBot,
  send: (id, text) => telegramBot.sendMessage(id, text),
  sendAdmin: (text) => telegramBot.sendAdminMessage(text),
  launch: () => telegramBot.launch()
};
