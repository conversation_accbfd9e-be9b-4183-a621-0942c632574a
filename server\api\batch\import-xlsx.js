#!/usr/bin/env node

require('dotenv').config({path: require('path').resolve(__dirname, '../../.env')});
const xlsx = require('xlsx');
const mysql = require('mysql2/promise');
const path = require('path');
const fs = require('fs');

// Database configuration from environment variables
const dbConfig = {
  host: process.env.DB_HOST || '*************',
  user: process.env.DB_USER || 'devusr',
  password: process.env.DB_PASSWORD || 'Simperum2019!',
  database: process.env.DB_NAME || 'perum',
  port: process.env.DB_PORT || 3307,
  multipleStatements: true
};

/**
 * Sanitize column names for MySQL compatibility
 * @param {string} name - Column name from Excel
 * @returns {string} - Sanitized column name
 */
function sanitizeColumnName(name) {
  if (!name || typeof name !== 'string') {
    return 'column_' + Math.random().toString(36).substr(2, 5);
  }
    
  return name
    .toString()
    .trim()
    .replace(/[^a-zA-Z0-9_]/g, '_')
    .replace(/^(\d)/, '_$1')
    .substring(0, 64);
}

/**
 * Determine MySQL data type based on Excel cell value
 * @param {*} value - Cell value
 * @returns {string} - MySQL data type
 */
function determineDataType(value) {
  if (value === null || value === undefined || value === '') {
    return 'VARCHAR(255)';
  }
    
  if (typeof value === 'number') {
    if (Number.isInteger(value)) {
      return 'BIGINT';
    } else {
      return 'DECIMAL(15,2)';
    }
  }
    
  if (typeof value === 'boolean') {
    return 'BOOLEAN';
  }
    
  if (value instanceof Date) {
    return 'DATETIME';
  }
    
  const strValue = String(value).trim();
    
  // Check for date formats
  if (/^\d{4}-\d{2}-\d{2}/.test(strValue) || /^\d{2}\/\d{2}\/\d{4}/.test(strValue)) {
    return 'DATETIME';
  }
    
  // Check for numeric strings
  if (/^-?\d+$/.test(strValue)) {
    return 'BIGINT';
  }
    
  if (/^-?\d*\.\d+$/.test(strValue)) {
    return 'DECIMAL(15,2)';
  }
    
  return 'TEXT';
}

/**
 * Get table structure from database
 * @param {mysql.Connection} connection - MySQL connection
 * @param {string} tableName - Table name
 * @returns {Promise<Array>} - Array of column information
 */
async function getTableStructure(connection, tableName) {
  try {
    const [rows] = await connection.execute(
      'DESCRIBE ' + tableName
    );
    return rows.map(row => ({
      name: row.Field,
      type: row.Type,
      null: row.Null === 'YES',
      key: row.Key,
      default: row.Default,
      extra: row.Extra
    }));
  } catch (error) {
    if (error.code === 'ER_NO_SUCH_TABLE') {
      return null;
    }
    throw error;
  }
}

/**
 * Compare Excel columns with table structure
 * @param {Array} excelColumns - Array of column names from Excel
 * @param {Array} tableColumns - Array of column information from database
 * @returns {boolean} - True if structures match
 */
function compareStructures(excelColumns, tableColumns) {
  if (excelColumns.length !== tableColumns.length) {
    return false;
  }
    
  for (let i = 0; i < excelColumns.length; i++) {
    const excelCol = sanitizeColumnName(excelColumns[i]);
    const tableCol = tableColumns[i].name;
        
    if (excelCol.toLowerCase() !== tableCol.toLowerCase()) {
      return false;
    }
  }
    
  return true;
}

/**
 * Create table based on Excel structure
 * @param {mysql.Connection} connection - MySQL connection
 * @param {string} tableName - Table name
 * @param {Array} columns - Array of column names from Excel
 * @param {Array} firstRow - First row data for type determination
 */
async function createTable(connection, tableName, columns, firstRow) {
  const columnDefinitions = columns.map((col, index) => {
    const sanitizedName = sanitizeColumnName(col);
    const dataType = firstRow && firstRow[index] !== undefined 
      ? determineDataType(firstRow[index]) 
      : 'VARCHAR(255)';
    return `\`${sanitizedName}\` ${dataType}`;
  });
    
  const createTableSQL = `
        CREATE TABLE IF NOT EXISTS \`${tableName}\` (
            id INT AUTO_INCREMENT PRIMARY KEY,
            ${columnDefinitions.join(',\n            ')},
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )
    `;
    
  console.log(createTableSQL)
  await connection.execute(createTableSQL);
}

/**
 * Insert data into table
 * @param {mysql.Connection} connection - MySQL connection
 * @param {string} tableName - Table name
 * @param {Array} columns - Array of column names
 * @param {Array} data - Array of data rows
 * @param {boolean} skipFirstRow - Whether to skip the first row
 */
async function insertData(connection, tableName, columns, data, skipFirstRow = false) {
  const sanitizedColumns = columns.map(col => sanitizeColumnName(col));
  const startIndex = skipFirstRow ? 1 : 0;
  const BATCH_SIZE = 500;
    
  if (startIndex >= data.length) {
    console.log('No data to insert after skipping header row');
    return;
  }
    
  const insertSQL = `
        INSERT INTO \`${tableName}\` (${sanitizedColumns.map(col => `\`${col}\``).join(', ')})
        VALUES ${Array(BATCH_SIZE).fill(`(${sanitizedColumns.map(() => '?').join(', ')})`).join(', ')}
    `;
    
  let insertedCount = 0;
  let totalRows = data.length - startIndex;
  let batchCount = 0;
    
  for (let i = startIndex; i < data.length; i += BATCH_SIZE) {
    const batchEnd = Math.min(i + BATCH_SIZE, data.length);
    const batchRows = data.slice(i, batchEnd);
    const actualBatchSize = batchRows.length;
    
    // Prepare values for bulk insert
    const values = [];
    for (const row of batchRows) {
      for (let j = 0; j < sanitizedColumns.length; j++) {
        const value = row[j];
        values.push(value === undefined || value === null ? null : value);
      }
    }
    
    // Adjust SQL for actual batch size
    const adjustedInsertSQL = actualBatchSize === BATCH_SIZE
      ? insertSQL
      : `
        INSERT INTO \`${tableName}\` (${sanitizedColumns.map(col => `\`${col}\``).join(', ')})
        VALUES ${Array(actualBatchSize).fill(`(${sanitizedColumns.map(() => '?').join(', ')})`).join(', ')}
      `;
    
    try {
      await connection.execute(adjustedInsertSQL, values);
      insertedCount += actualBatchSize;
      batchCount++;
      
      if (batchCount % 10 === 0 || i + BATCH_SIZE >= data.length) {
        console.log(`Progress: ${insertedCount}/${totalRows} rows inserted (${Math.round((insertedCount/totalRows)*100)}%)`);
      }
    } catch (error) {
      console.error(`Error inserting batch starting at row ${i + 1}:`, error.message);
      
      // Fallback to individual row insertion for this batch
      console.log('Falling back to individual row insertion for this batch...');
      const singleInsertSQL = `
        INSERT INTO \`${tableName}\` (${sanitizedColumns.map(col => `\`${col}\``).join(', ')})
        VALUES (${sanitizedColumns.map(() => '?').join(', ')})
      `;
      
      for (let j = 0; j < batchRows.length; j++) {
        const row = batchRows[j];
        const singleValues = sanitizedColumns.map((_, index) => {
          const value = row[index];
          return value === undefined || value === null ? null : value;
        });
        
        try {
          await connection.execute(singleInsertSQL, singleValues);
          insertedCount++;
        } catch (singleError) {
          console.error(`Error inserting individual row ${i + j + 1}:`, singleError.message);
          console.error('Row data:', singleValues);
        }
      }
    }
  }
    
  console.log(`Successfully inserted ${insertedCount} rows into ${tableName} in ${batchCount} batches`);
}

/**
 * Check if path is a directory
 * @param {string} filePath - Path to check
 * @returns {boolean} - True if directory, false otherwise
 */
function isDirectory(filePath) {
  try {
    return fs.statSync(filePath).isDirectory();
  } catch (error) {
    return false;
  }
}

/**
 * Get all xlsx files from a directory
 * @param {string} dirPath - Directory path
 * @returns {Array} - Array of xlsx file paths
 */
function getXlsxFilesFromDirectory(dirPath) {
  try {
    const files = fs.readdirSync(dirPath);
    return files
      .filter(file => {
        const ext = path.extname(file).toLowerCase();
        return ext === '.xlsx' || ext === '.xls';
      })
      .map(file => path.join(dirPath, file));
  } catch (error) {
    console.error(`Error reading directory ${dirPath}:`, error.message);
    return [];
  }
}

/**
 * Import multiple xlsx files from a directory
 * @param {string} dirPath - Directory path containing xlsx files
 * @param {string} tableName - Target table name (will be used as prefix)
 */
async function importXlsxDirectory(dirPath, tableName) {
  const xlsxFiles = getXlsxFilesFromDirectory(dirPath);
  
  if (xlsxFiles.length === 0) {
    console.log(`No xlsx files found in directory: ${dirPath}`);
    return;
  }
  
  console.log(`Found ${xlsxFiles.length} xlsx files in directory: ${dirPath}`);
  
  for (const filePath of xlsxFiles) {
    const fileName = path.basename(filePath, path.extname(filePath));
    // const tableNameWithFile = `${tableName}_${fileName}`.replace(/[^a-zA-Z0-9_]/g, '_');
    
    console.log(`\nProcessing file: ${filePath}`);
    // console.log(`Target table: ${tableNameWithFile}`);
    
    await importXlsxToMySQL(filePath, tableName);
  }
}

/**
 * Main import function
 * @param {string} xlsxFilePath - Path to XLSX file or directory
 * @param {string} tableName - Target table name
 */
async function importXlsxToMySQL(xlsxFilePath, tableName) {
  let connection;
    
  try {
    // Validate file/directory exists
    if (!fs.existsSync(xlsxFilePath)) {
      throw new Error(`File or directory not found: ${xlsxFilePath}`);
    }
        
    // Check if it's a directory
    if (isDirectory(xlsxFilePath)) {
      await importXlsxDirectory(xlsxFilePath, tableName);
      return;
    }
        
    // Validate file extension
    if (!xlsxFilePath.toLowerCase().endsWith('.xlsx') && !xlsxFilePath.toLowerCase().endsWith('.xls')) {
      throw new Error('File must be an Excel file (.xlsx or .xls)');
    }
        
    // Read Excel file
    console.log(`Reading Excel file: ${xlsxFilePath}`);
    const workbook = xlsx.readFile(xlsxFilePath);
    const sheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[sheetName];
        
    // Convert to JSON
    const data = xlsx.utils.sheet_to_json(worksheet, {header: 1, defval: null});
        
    if (data.length === 0) {
      throw new Error('Excel file is empty');
    }
        
    // Extract headers and data
    const headers = data[0];
    console.log(`Found ${headers.length} columns:`, headers);
    console.log(`Found ${data.length} total rows`);
        
    // Connect to database
    console.log('Connecting to MySQL database...');
    connection = await mysql.createConnection(dbConfig);
        
    // Check if table exists
    const tableStructure = await getTableStructure(connection, tableName);
        
    if (tableStructure) {
      console.log(`Table '${tableName}' already exists`);
            
      // Validate table structure
      if (!compareStructures(headers, tableStructure.filter(c => !['id','created_at','updated_at'].includes(c.name)))) {
        throw new Error(
          `Table structure mismatch. Excel columns: ${headers.map(sanitizeColumnName).join(', ')}, \n` +
                    `Table columns: ${tableStructure.map(col => col.name).join(', ')}`
        );
      }
            
      console.log('Table structure matches. Skipping first row and inserting data...');
      await insertData(connection, tableName, headers, data, true);
    } else {
      console.log(`Table '${tableName}' does not exist. Creating table...`);
            
      // Create table with first row as column names
      await createTable(connection, tableName, headers, data[1] || null);
      console.log(`Table '${tableName}' created successfully`);
            
      // Insert all data including first row as data
      await insertData(connection, tableName, headers, data, false);
    }
        
    console.log('Import completed successfully');
        
  } catch (error) {
    console.error('Error during import:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// Main execution
if (require.main === module) {
  const args = process.argv.slice(2);
    
  if (args.length < 2) {
    console.error('Usage: node import_xlsx.js <xlsx_file_path_or_directory> <table_name>');
    console.error('Examples:');
    console.error('  Single file: node import_xlsx.js ./data/customers.xlsx customers');
    console.error('  Directory:   node import_xlsx.js ./data/customers_dir customers');
    process.exit(1);
  }
    
  const [xlsxFilePath, tableName] = args;
    
  // Validate table name
  if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(tableName)) {
    console.error('Invalid table name. Use only letters, numbers, and underscores, starting with a letter or underscore.');
    process.exit(1);
  }
    
  importXlsxToMySQL(xlsxFilePath, tableName);
}

module.exports = {importXlsxToMySQL, sanitizeColumnName, determineDataType};