<template>
  <Modal title="Material" :show.sync="xshow" width="400px" @onSubmit="Save">
    <div></div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    xshow: false,
  }),
  props: {
    show: <PERSON>ole<PERSON>,
  },
  watch: {
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      this.$emit('update:show', val)
    },
  },
  methods: {
    async Save() {
      let ret = await this.$api.call('', {})
      if (ret.success) this.$emit('update:show', false)
    },
  },
}
</script>
