import js from '@eslint/js';
import vue from 'eslint-plugin-vue';
import prettierConfig from '@vue/eslint-config-prettier';

export default [
  { files: ['**/*.{js,mjs,cjs,ts,mts,cts,vue}'] },
  js.configs.recommended,
  ...vue.configs['flat/essential'],
  prettierConfig,
  {
    rules: {
      'prettier/prettier': [
        'error',
        {
          singleQuote: true,
          semi: false,
        },
      ],
      'vue/multi-word-component-names': 'off',
    },
  },
];