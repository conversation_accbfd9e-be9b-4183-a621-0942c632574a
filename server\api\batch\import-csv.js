// import-csv.js
require('dotenv').config({path: require('path').resolve(__dirname, '../../.env')});
const fs = require('fs');
const readline = require('readline');
const mysql = require('mysql');
const {parse} = require('csv-parse/sync');
const {camelCase} = require('lodash');

if (process.argv.length < 3) {
  console.error('Usage: node import-csv.js <csv-file-path>');
  process.exit(1);
}

const csvPath = process.argv[2];

// Prompt for table name
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function askTableName() {
  return new Promise(resolve => {
    rl.question('Enter MySQL table name to import into: ', answer => {
      resolve(answer.trim());
    });
  });
}

// MySQL config (edit as needed)
const db = mysql.createPool({
  host: process.env.DB_HOST || 'localhost',
  user: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  database: process.env.DB_NAME || 'test',
  port: process.env.DB_PORT || 3306,
  connectionLimit: 10
});

function createTableIfNotExists(table, columns) {
  const cols = columns.map(col => `\`${col}\` TEXT`).join(', ');
  const sql = `CREATE TABLE IF NOT EXISTS \`${table}\` (${cols})`;
  return new Promise((resolve, reject) => {
    db.query(sql, err => (err ? reject(err) : resolve()));
  });
}

function insertBatch(table, columns, rows) {
  if (!rows.length) return Promise.resolve();
  const placeholders = rows.map(() => `(${columns.map(() => '?').join(',')})`).join(',');
  const values = rows.flat().map(v => v || null);
  const sql = `INSERT INTO \`${table}\` (${columns.map(c => `\`${c}\``).join(',')}) VALUES ${placeholders}`;
  return new Promise((resolve, reject) => {
    db.query(sql, values, err => (err ? reject(err) : resolve()));
  });
}

const parseStream = require('csv-parse').parse;

(async () => {
  try {
    // Read first line for headers
    const fileStream = fs.createReadStream(csvPath);
    const rlFile = readline.createInterface({input: fileStream});
    const firstLine = await new Promise(resolve => rlFile.once('line', resolve));
    rlFile.close();
    fileStream.close();

    // Parse headers and convert to camelCase
    let rawHeaders = parse(firstLine, {delimiter: ',', to_line: 1})[0];
    // Assign alias c1, c2, ... for empty column names
    rawHeaders = rawHeaders.map((h, i) => {
      const trimmed = (h || '').trim();
      // return trimmed ? camelCase(trimmed) : `c${i+1}`;
      return trimmed || `c${i+1}`;
    });
    const columns = rawHeaders;

    const table = await askTableName();
    await createTableIfNotExists(table, columns);

    // Stream and batch insert
    const input = fs.createReadStream(csvPath);
    const parser = parseStream({columns: rawHeaders, from_line: 2, relax_column_count: true, trim: true});
    let batch = [];
    let rowCount = 0;

    parser.on('readable', async function () {
      let record;
      while ((record = parser.read())) {
        batch.push(columns.map(col => record[col] || ''));
        if (batch.length === 500) {
          parser.pause();
          await insertBatch(table, columns, batch);
          rowCount += batch.length;
          console.log(`Inserted ${rowCount} rows...`);
          batch = [];
          parser.resume();
        }
      }
    });

    parser.on('end', async function () {
      if (batch.length) {
        await insertBatch(table, columns, batch);
        rowCount += batch.length;
        console.log(`Inserted ${rowCount} rows (final batch).`);
      }
      db.end();
      rl.close();
      console.log('Import complete.');
    });

    parser.on('error', err => {
      console.error('CSV parse error:', err);
      db.end();
      rl.close();
    });

    input.pipe(parser);
  } catch (err) {
    console.error('Error:', err);
    db.end();
    rl.close();
  }
})();