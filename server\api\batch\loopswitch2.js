require('dotenv').config({path: require('path').resolve(__dirname, '../../.env')});
console.log(require('path').resolve(__dirname, '../../.env'))
const fs = require('fs');
const csv = require('csv-parse/sync');
const {stringify} = require('csv-stringify/sync');
const mysql = require('mysql');
const util = require('util');
var readlines = require("n-readlines");

// MySQL Configuration with improved timeout settings
const pool = mysql.createPool({
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '20'),
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '3307'),
  // acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
  // connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT || '60000'),
  // timeout: parseInt(process.env.DB_TIMEOUT || '60000'),
  waitForConnections: true,
  queueLimit: 0
});

// Function to get a connection with retry logic
const getConnection = () => {
  return new Promise((resolve, reject) => {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error('Error getting MySQL connection:', err);
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
          console.log('Retrying connection in 2 seconds...');
          setTimeout(() => {
            getConnection().then(resolve).catch(reject);
          }, 2000);
        } else {
          reject(err);
        }
      } else {
        // Set session variables to increase timeouts
        connection.query('SET SESSION wait_timeout=300', (err) => {
          if (err) console.error('Error setting wait_timeout:', err);

          connection.query('SET SESSION interactive_timeout=300', (err) => {
            if (err) console.error('Error setting interactive_timeout:', err);
            resolve(connection);
          });
        });
      }
    });
  });
};

// Function to execute a query with retry logic
const executeQuery = async (sql, params, retries = 3) => {
  let connection;
  try {
    connection = await getConnection();
    const query = util.promisify(connection.query).bind(connection);
    return await query(sql, params);
  } catch (error) {
    if (retries > 0 && (error.code === 'PROTOCOL_CONNECTION_LOST' ||
      error.code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR' ||
      error.message.includes('timeout'))) {
      console.log(`Query failed, retrying... (${retries} attempts left)`);
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      return executeQuery(sql, params, retries - 1);
    }
    throw error;
  } finally {
    if (connection) connection.release();
  }
};

// Process records in batches
async function run(row) {

  // console.log(row.NIK, row.Kabupaten, row.Kecamatan, row.Desa, row.TipeData);
  const recs = await executeQuery(`select * from tmp_1022
    where Kabupaten = '${row.Kabupaten}' AND COALESCE(Intervensi,Intervensi2,Intervensi3) IS NULL 
    LIMIT 1`)
    
  if (recs.length) {
    await executeQuery(`UPDATE tmp_1022 SET Intervensi2 = '${row.Intervensi}', NIK2 = '${row.NIK}', NIKENC2 = AES_ENCRYPT('${row.NIK}', '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5') 
      WHERE NIK = '${recs[0].NIK}' AND Kabupaten = '${row.Kabupaten}'`)
    return
  } else {
    console.log('Nothing to switch')
  }

  return null;
}

async function main() {
  try {

    // await readCSV()

    const records = await executeQuery(`select
          CAST(AES_DECRYPT(p.NIK, '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5') AS CHAR) NIK,
          p.Kabupaten,
          'APBN PROV 2025' Intervensi
    from prm_bansos p
      left join tmp_1022 t1
      on p.NIK = t1.NIKENC
      left join tmp_1022 t2
      on p.NIK = t2.NIKENC2
      left join tmp_1022 t3
      on p.NIK = t3.NIKENC3
    where p.Tahun = 2025 AND p.Tahapan = 'REKOM 2' 
        AND t1.NIK IS NULL AND t2.NIK2 IS NULL AND t3.NIK3 IS NULL AND p.NIK IS NOT NULL`);

    // const records = [{ Kabupaten: 'KLATEN' }]
    // console.log(records)
    let idx = 1
    for (const row of records) {
      console.log(idx+'/'+records.length, row.NIK, row.Kabupaten);
      await run(row)
      idx++
    }

    console.log('processed successfully');

    // End MySQL pool
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(0);
    });

    process.exit(0)
  } catch (error) {
    console.error('Error:', error);

    // End MySQL pool on error
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(1);
    });
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('Process interrupted, closing connections...');
  pool.end(err => {
    if (err) console.error('Error closing MySQL pool:', err);
    process.exit(2);
  });
});

main();