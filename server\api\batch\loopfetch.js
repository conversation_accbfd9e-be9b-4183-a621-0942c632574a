require('dotenv').config({path: require('path').resolve(__dirname, '../../.env')});
console.log(require('path').resolve(__dirname, '../../.env'))
const fs = require('fs');
const csv = require('csv-parse/sync');
const {stringify} = require('csv-stringify/sync');
const mysql = require('mysql');
const util = require('util');
const axios = require('axios');
const querystring = require('querystring');
var readlines = require("n-readlines");

// MySQL Configuration with improved timeout settings
const pool = mysql.createPool({
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '2'),
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '3307'),
  // acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
  // connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT || '60000'),
  // timeout: parseInt(process.env.DB_TIMEOUT || '60000'),
  waitForConnections: true,
  queueLimit: 0
});

pool.getConnection((err, connection) => {
  if (err) {
    if (err.code === "PROTOCOL_CONNECTION_LOST") {
      console.error("Database connection was closed.");
    }
    if (err.code === "ER_CON_COUNT_ERROR") {
      console.error("Database has too many connections.");
    }
    if (err.code === "ECONNREFUSED") {
      console.error("Database connection was refused.");
    }
  }
  if (connection) connection.release();
  return;
});
pool.query = util.promisify(pool.query); // Magic happens here.


// Process records in batches
async function run(row) {
  // console.log(row.NIK)
  let ret;
  let retries = 3;
  while (retries > 0) {
    try {
      ret = await axios.post('https://my.pkp.go.id/cekbantuan', querystring.stringify({
        j:1,
        nik:row.NIK
      }), {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          cookie: 'PHPSESSID=cu5rrqqarnkrrrpflq1olr7q77'
        }
      });
      break; // Success, exit the retry loop
    } catch (error) {
      retries--;
      if (retries === 0 || (error.code !== 'ECONNRESET' && !error.message.includes('ECONNRESET'))) {
        // If no retries left or not a connection reset error, re-throw
        throw error;
      }
      console.log(`Retrying request for NIK: ${row.NIK}. Retries left: ${retries}`);
      // Optional: Add a small delay before retrying
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
  // console.log(ret.data)
  
  // Extract columns 4 and 5 from the HTML table
  const extractedData = extractColumns4And5(ret.data);
  console.log(row.NIK, extractedData)
  // console.log('Extracted Column 4:', extractedData.column4);
  // console.log('Extracted Column 5:', extractedData.column5);

  if(extractedData.column5) {
    await pool.query(`update tmp_1022final set StatusPKP = '${extractedData.column5}' WHERE NIK = '${row.NIK}'`)
  }
  
  return extractedData;
}

// Function to extract columns 4 and 5 from HTML table string
function extractColumns4And5(htmlString) {
  // Regular expression to match td elements
  const tdRegex = /<td[^>]*>(.*?)<\/td>/g;
  const matches = [];
  let match;
  
  // Extract all td elements
  while ((match = tdRegex.exec(htmlString)) !== null) {
    matches.push(match[1]);
  }
  
  let col5 = (matches[6] || '')
  if (col5.match('sudah pernah menerima')) {
    col5 = col5.split('<br>')[0].replace('sudah pernah menerima bantuan ','').replace('tahun ','')
  } else {
    col5 = ''
  }
  // Return columns 4 and 5 (index 3 and 4)
  return {
    column4: matches[5] || '',
    column5: col5
  };
}

async function main() {
  try {
    process.env.NODE_TLS_REJECT_UNAUTHORIZED = '0';
    // const records = await pool.query(`select NIK from tmp_1022final where NIK >= 3309154909890001`);
    const records = [{NIK:3306131405790007}]
    // const records = [{ Kabupaten: 'KLATEN' }]
    // console.log(records)
    let i = 0
    for (const row of records) {
      await run(row)
      i++
      if (i%100 == 0) {
        console.log(i,'/',records.length)
      }
      // console.log('Final extracted data:', extractedData)
    }

    console.log('processed successfully');

    // End MySQL pool
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(0);
    });

    process.exit(0)
  } catch (error) {
    console.error('Error:', error);

    // End MySQL pool on error
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(1);
    });
  }
}

// Handle process termination
// process.on('SIGINT', () => {
//   console.log('Process interrupted, closing connections...');
//   pool.end(err => {
//     if (err) console.error('Error closing MySQL pool:', err);
//     process.exit(2);
//   });
// });

main();