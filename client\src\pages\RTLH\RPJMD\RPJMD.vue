<template>
  <Page title="Database PSN">
    <template v-slot:toolbar>
      <v-menu offset-y>
        <template v-slot:activator="{ on }">
          <v-btn text v-on="on" :loading="loadingPrint" small>
            <v-icon>mdi-microsoft-excel</v-icon>
          </v-btn>
        </template>
        <v-list dense style="width: 250px">
          <v-list-item @click="Download('BNBA')">
            <v-list-item-icon>
              <v-icon>mdi-microsoft-excel</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>Laporan BNBA</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
          <v-list-item @click="Download('Rekap')">
            <v-list-item-icon>
              <v-icon>mdi-microsoft-excel</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>Laporan Rekap</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list>
      </v-menu>
      <!-- <v-icon @click="showCariNIK = true">mdi-account-search</v-icon>
      <v-icon @click="showMap = !showMap">mdi-map-search</v-icon>
      <v-icon @click="BeforePrint()">print</v-icon> -->
      <!-- <v-btn small @click="showCariNIK = true">
        <v-icon left>mdi-account-search</v-icon>
        CARI NIK
      </v-btn> -->
    </template>
    <Grid
      id="table-database-rtlh"
      :datagrid.sync="backlog"
      dbref="PRM.RPJMD"
      :dbparams.sync="filters"
      class="dense"
      :preHead="true"
      :disabled="true"
      :height="'calc(100vh - 120px)'"
      v-show="!showMap"
      :doRebind="pbdtRebind"
      :columns="[
        {
          name: 'NIK',
          value: 'NIK',
          filter: {
            type: 'search',
            value: 'Keyword',
          },
        },
        {
          name: 'Nama',
          value: 'Nama',
          width: '200px',
          class: 'plain fix-width',
          filter: {
            type: 'search',
            value: 'Keyword',
          },
        },
        {
          name: 'Alamat',
          value: 'Alamat',
          width: '250px',
          class: 'fix-width',
          filter: {
            type: 'search',
            value: 'Alamat',
          },
        },
        {
          name: 'Kabupaten',
          value: 'Kabupaten',
          filter: {
            type: 'select',
            value: 'KabupatenID',
            text: 'Kabupaten',
            dbref: 'Arch.SelArea',
            dbparams: { ParentAreaID: 33 },
          },
        },
        {
          name: 'Kecamatan',
          value: 'Kecamatan',
          filter: {
            type: 'select',
            value: 'KecamatanID',
            text: 'Kecamatan',
            dbref: 'Arch.SelArea',
            dbparams: (f) => ({ ParentAreaID: f.KabupatenID }),
          },
        },
        {
          name: 'Kelurahan',
          value: 'Kelurahan',
          filter: {
            type: 'select',
            value: 'KelurahanID',
            text: 'Kelurahan',
            dbref: 'Arch.SelArea',
            dbparams: (f) => ({ ParentAreaID: f.KecamatanID }),
          },
        },
        {
          name: 'Intervensi',
          value: 'Intervensi',
          filter: {
            type: 'select',
            value: 'Sumber',
            text: 'SumberDana',
            dbref: 'PRM.SelSumber',
          },
        },
        {
          name: 'Status PKP',
          value: 'StatusPKP',
        },
        {
          name: 'Desil',
          value: 'desil',
        },
        {
          name: 'Menggantikan',
          value: 'Menggantikan',
          hide: true,
        },
      ]"
      :doPrint="doPrint"
      @after-bind="AfterGridBind"
    >
      <template v-slot:pre-head="{ allfilters, openColumnSetting }">
        <Panel dbref="PRM.SelDescRPJMD" :dbparams="allfilters">
          <template v-slot="{ first }">
            <div style="display: flex" class="pre-head">
              <div style="padding: 8px 12px; flex: 2; font-size: 14px">
                <XInput
                  rightIcon="search"
                  width="230px"
                  placeholder="Cari NIK/Nama .."
                  :value.sync="keyword"
                />
                <!-- <v-btn @click="showFilters = true">
                  <v-icon left>mdi-table-search</v-icon>
                  Pencarian
                </v-btn> -->
              </div>
              <div class="table-desc" style="">
                <v-btn text small color="gray" @click="openColumnSetting">
                  <v-icon left>mdi-cog</v-icon>
                  ATUR KOLOM
                </v-btn>
                <div style="background-color: #95a5a6">
                  Total: {{ first.Total | format }}
                </div>
                <div style="background-color: #2ecc71">
                  Intervensi {{ first.Intervensi | format }}
                </div>
                <div style="background-color: #ecf0f1; color: gray">
                  Sisa: {{ first.Sisa | format }}
                </div>
              </div>
            </div>
          </template>
        </Panel>
      </template>
      <template v-slot:row-NIK="{ row }">
        <nik-block :nik="row.NIK" />
      </template>
      <template v-slot:row-Nama="{ row }">
        <v-btn text small color="primary" @click.stop="OpenDetail(row.NIK)">
          {{ row.Nama }}
        </v-btn>
      </template>
      <template v-slot:row-Prioritas="{ row }">
        <v-icon
          :color="priorcolors[row.Prioritas]"
          v-tooltip="`Prioritas ${row.Prioritas}`"
        >
          mdi-numeric-{{ row.Prioritas }}-circle
        </v-icon>
      </template>
      <template v-slot:row-VerStatsID="{ row }">
        <v-icon
          v-if="row.VerStatsID >= 6"
          color="primary"
          v-tooltip="'Sudah Terverifikasi'"
        >
          mdi-account-check
        </v-icon>
        <v-icon v-if="row.VerStatsID < 6" v-tooltip="'Belum Terverifikasi'">
          mdi-account-question-outline
        </v-icon>
      </template>
      <template v-slot:row-NamaData="{ row }">
        <span>{{ row.NamaData }}</span>
        <span
          v-if="row.DataTag"
          style="
            font-size: 10px;
            margin-left: 5px;
            background: lightblue;
            padding: 3px;
            border-radius: 3px;
          "
        >
          {{ row.DataTag }}
        </span>
      </template>
      <template v-slot:row-GeoLoc="{ row }">
        <v-icon
          v-tooltip="'Lihat Lokasi pada Peta'"
          @click.stop="OpenMap(row.NoUrutRT)"
        >
          mdi-map-marker-radius
        </v-icon>
      </template>
    </Grid>
    <DBMap
      v-show="showMap"
      dbref="PRM.SelPBDTMap"
      :dbparams="filters"
      :noRef="selectedRef"
      :disabled="true"
    />
    <ValidasiDetail
      :show.sync="showDetailModal"
      :nik="selectedRef"
      :disabled="true"
    />
    <ImportExcel :show.sync="showImportExcel" />
    <ImportERTLH :show.sync="showImportERTLH" />
    <ImportDinsos :show.sync="showImportDinsos" />
  </Page>
</template>
<script>
// import ReportViewer from '../../../components/ReportViewer.vue'
import ValidasiDetail from '../InputUsulan/ValidasiDetail.vue'
import DBMap from './Map.vue'
import ImportExcel from './ImportExcel.vue'
import ImportERTLH from './ImportERTLH.vue'
import ImportDinsos from './ImportDinsos.vue'

export default {
  components: {
    ValidasiDetail,
    // ReportViewer,
    DBMap,
    ImportExcel,
    ImportERTLH,
    ImportDinsos,
  },
  data: () => ({
    priorcolors: ['#333', '#e53935', '#F4511E', '#43A047', '#1E88E5'],
    showDetailModal: false,
    showCariNIK: false,
    showImportNIK: false,
    importNIKText: 'IMPORT',
    showImportExcel: false,
    showImportERTLH: false,
    showImportDinsos: false,
    importStatus: '',
    showMap: false,
    filters: {},
    gridFilters: {},
    showFilters: false,
    selectedRef: null,
    lat: null,
    lon: null,
    backlog: [],
    reportUrl: null,
    doPrint: 0,
    reportOptions: {},
    multipleNIK: null,
    keyword: '',
    pbdtRebind: 1,
    loading: false,
    loadingPrint: false,
  }),
  async mounted() {},
  watch: {
    keyword(val) {
      this.filters.Keyword = val
      this.pbdtRebind++
    },
  },
  methods: {
    OpenDetail(noRef) {
      console.log(noRef)
      this.selectedRef = noRef
      this.showDetailModal = true
    },
    OpenMap(noRef) {
      this.selectedRef = noRef
      this.showMap = true
    },
    OpenImportExcel() {
      this.showImportExcel = true
    },
    OpenImportERTLH() {
      this.showImportERTLH = true
    },
    OpenImportDinsos() {
      this.showImportDinsos = true
    },
    OpenImportByNIK() {
      this.showImportNIK = true
    },
    AfterGridBind(filters) {
      this.gridFilters = filters
    },
    async Download(mode) {
      this.loadingPrint = true
      let ret = await this.$api.post(`/report/Generic/xlsx`, {
        sp: `PRM_Rpt${mode}RPJMD`,
      })
      this.loadingPrint = false
      this.$api.download(this.$api.url + '/report' + ret.data)
    },
    sleep(ms) {
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve()
        }, ms)
      })
    },
    async ApplyFilters() {},
  },
}
</script>
<style lang="scss">
.page-database-psn {
  .page-content {
    overflow: auto;
  }
  .table-desc {
    flex: 1;
    display: flex;
    align-items: flex-end;
    justify-content: flex-end;

    div {
      padding: 3px 5px;
      margin-left: 3px;
      color: white;
    }
  }
}
.v-list--dense {
  .v-list-item__icon {
    margin-right: 20px !important;
  }
}
</style>
