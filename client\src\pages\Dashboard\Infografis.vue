<template>
  <svg
    id="infografis"
    xmlns="http://www.w3.org/2000/svg"
    xml:space="preserve"
    width="11.6929in"
    height="8.26772in"
    version="1.1"
    style="
      shape-rendering: geometricPrecision;
      text-rendering: geometricPrecision;
      image-rendering: optimizeQuality;
      fill-rule: evenodd;
      clip-rule: evenodd;
    "
    viewBox="0 0 11692.91 8267.72"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    xmlns:xodm="http://www.corel.com/coreldraw/odm/2003"
  >
    <defs>
      <component :is="'style'">
        @font-face { font-family: 'Plat Nomor'; font-variant: normal;
        font-style: normal; font-weight: normal; src: url('#FontID0')
        format(svg); }
      </component>
      <filter id="shadow">
        <feDropShadow
          dx="40"
          dy="40"
          stdDeviation="40"
          flood-color="rgba(0,0,0,0.5)"
        />
      </filter>
      <font
        id="FontID0"
        horiz-adv-x="464"
        font-variant="normal"
        style="fill-rule: nonzero"
        font-style="normal"
        font-weight="400"
      >
        <font-face font-family="Plat Nomor">
          <font-face-src>
            <font-face-name name="Plat Nomor" />
          </font-face-src>
        </font-face>
        <missing-glyph><path d="M0 0z" /></missing-glyph>
        <glyph
          unicode="+"
          horiz-adv-x="423"
          d="M45.0004 252.994l126.993 0 0 -126.993 81.0006 0 0 126.993 126.001 0 0 81.0006 -126.001 0 0 126.001 -81.0006 0 0 -126.001 -126.993 0 0 -81.0006z"
        />
        <glyph
          unicode="-"
          horiz-adv-x="435"
          d="M51.0004 162.993l333.995 0 0 81.0006 -333.995 0 0 -81.0006z"
        />
        <glyph
          unicode="."
          horiz-adv-x="190"
          d="M51.9954 84.0066l0 -84.0066 86.0111 0 0 84.0066 -86.0111 0z"
        />
        <glyph
          unicode="0"
          horiz-adv-x="483"
          d="M347.011 579.005l0 -416.011 -163.017 485.012c18.0001,10.9843 40.0161,15.9923 62.0084,15.9923 50.0083,0 101.009,-27.9923 101.009,-84.9928zm-288.002 0l0 -423.995c0,-110.009 93.0007,-165.001 186.994,-165.001 93.9929,0 183.994,54.9926 186.994,165.001l0 423.995c-1.98427,109.985 -93.0007,165.001 -186.994,165.001 -93.9929,0 -186.994,-55.0162 -186.994,-165.001zm85.9849 -11.008l162.001 -481.988c-18.0001,-10.0158 -39.0003,-16.0159 -60.9926,-16.0159 -50.0083,0 -101.009,28.016 -101.009,85.0164l0 412.988z"
        />
        <glyph
          unicode="1"
          horiz-adv-x="251"
          d="M108 622.993l0 -622.993 85.9908 0 0 734.01 -85.9908 0 -90.9919 -93.0167 55.9841 -53.9999 35.0078 35.9999z"
        />
        <glyph
          unicode="2"
          horiz-adv-x="435"
          d="M138.993 81.0006l198.002 297.002c41.0082,54.0004 63.0005,117.993 63.0005,177.001 0,100.985 -57.0004,186.994 -180.994,186.994 -84.9928,0 -167.009,-51.9925 -168.993,-162.993l85.9849 0c0,57.0004 39.0003,84.0007 82.0164,84.0007 72.0006,0 99.9929,-49.0161 99.9929,-108.001 0,-48.0004 -27.9923,-99.0008 -57.0004,-141.993l-222.994 -332.01 0 -81.0006 361.987 0 0 81.0006 -261.002 0z"
        />
        <glyph
          unicode="3"
          horiz-adv-x="479"
          d="M348.995 245.01l0 -90.0007c0,-57.0004 -54.9926,-85.0164 -111.001,-85.0164 -49.9846,0 -100.985,28.016 -100.985,85.0164l-86.0086 0c3.00002,-110.009 93.0007,-165.001 186.994,-165.001 98.0086,0 197.009,54.9926 197.009,165.001l0 90.0007c0,55.9847 -33.9924,106.985 -89.0086,132.993 49.0161,27.0002 79.0164,75.9927 79.0164,127.985l0 73.0163c0,109.985 -93.0007,165.001 -187.017,165.001 -93.9929,0 -184.986,-55.0162 -186.994,-165.001l86.0086 0c0,57.0004 51.0004,84.9928 100.985,84.9928 50.0083,0 101.009,-27.9923 101.009,-84.9928l0 -73.0163c0,-50.9768 -38.0082,-89.9771 -86.0086,-89.9771l-126.001 0 0 -81.0006 136.017 0c48.0004,0 85.9849,-39.0003 85.9849,-90.0007l0 0z"
        />
        <glyph
          unicode="4"
          horiz-adv-x="465"
          d="M305.999 334.001l0 -123.003 -182.004 0 277.005 523.011 -95.993 0 -267.003 -523.011 0 -80.9897 85.9908 0 182.004 0 0 -130.009 85.9908 0 0 130.009 40.0089 0 0 80.9897 -40.0089 0 0 123.003 -85.9908 0z"
        />
        <glyph
          unicode="5"
          horiz-adv-x="485"
          d="M144 154.994l-85.9908 0c0,-109.984 92.9964,-164.996 186.985,-164.996 97.9975,0 192.998,55.0122 197.007,164.996l0 179.007c0,110.004 -99.0099,164.996 -197.007,164.996 -34.9875,0 -70.9875,-7.99773 -100.994,-23.9932l0 177.995 267.995 0 0 81.0099 -353.986 0 0 -81.0099 0 -318.998 85.9908 0c0,56.9965 51.0033,84.9987 100.994,84.9987 56.0044,0 110.996,-28.0022 110.996,-84.9987l0 -179.007c0,-56.9965 -54.992,-84.9987 -110.996,-84.9987 -49.9909,0 -100.994,28.0022 -100.994,84.9987z"
        />
        <glyph
          unicode="6"
          horiz-adv-x="486"
          d="M442.993 154.994l0 128.996c-2.99662,110.004 -98.9897,165.017 -196.987,165.017 -35.0078,0 -71.0077,-7.00561 -101.014,-23.0011l0 152.99c0,56.9965 51.0033,84.9987 101.014,84.9987 49.9909,0 100.994,-28.0022 100.994,-84.9987l85.9908 0c-1.98425,110.004 -92.9964,164.996 -186.985,164.996 -94.0088,0 -187.005,-54.992 -187.005,-164.996l0 -295.005 0 -118.994 0 -10.0022c0,-109.984 92.9964,-164.996 187.005,-164.996 97.9975,0 192.998,55.0122 196.987,164.996zm-298.002 10.0022l0 118.994c0,57.0167 51.0033,85.0189 101.014,85.0189 55.9841,0 110.996,-28.0022 110.996,-85.0189l0 -128.996c0,-56.9965 -55.0122,-84.9987 -110.996,-84.9987 -50.0111,0 -101.014,28.0022 -101.014,84.9987l0 10.0022z"
        />
        <glyph
          unicode="7"
          horiz-adv-x="405"
          d="M107.008 63.01l0 -63.01 85.9908 0 0 63.01c0,234.992 177.003,559.983 177.003,559.983l0 111.017 -86.0111 0 -267.995 0 0 -81.0099 277.997 0c0,0 -186.985,-369.009 -186.985,-589.99z"
        />
        <glyph
          unicode="8"
          horiz-adv-x="493"
          d="M267.003 416.004l-30.0067 0c-47.9864,0 -85.9908,38.9965 -85.9908,89.9998l0 72.992c0,56.9965 51.0033,84.9987 100.994,84.9987 49.9909,0 100.994,-28.0022 100.994,-84.9987l0 -72.992c0,-51.0033 -37.9842,-89.9998 -85.9908,-89.9998zm95.993 -35.9999c46.9943,26.9898 76.0088,74.9965 76.0088,126l0 72.992c0,110.004 -92.9964,164.996 -187.005,164.996 -94.0088,0 -187.005,-54.992 -187.005,-164.996l0 -72.992c0,-51.0033 29.0146,-99.0099 76.0088,-126l0 -2.99662c-53.0078,-25.9977 -86.0111,-77.001 -86.0111,-132.013l0 -89.9998c0,-109.984 99.0099,-164.996 197.007,-164.996 97.9975,0 197.007,55.0122 197.007,164.996l0 89.9998c0,55.0122 -33.0033,106.016 -86.0111,132.013l0 2.99662zm-136.002 -45.01l50.0111 0c47.9864,0 85.9908,-38.9965 85.9908,-89.9998l0 -89.9998c0,-56.9965 -54.992,-84.9987 -110.996,-84.9987 -56.0044,0 -110.996,28.0022 -110.996,84.9987l0 89.9998c0,51.0033 38.0044,89.9998 85.9908,89.9998z"
        />
        <glyph
          unicode="9"
          horiz-adv-x="485"
          d="M49.9909 578.995l0 -128.996c3.01687,-110.004 99.0099,-164.996 197.007,-164.996 35.0078,0 71.0077,7.00561 100.994,23.0011l0 -153.01c0,-56.9965 -50.983,-84.9987 -100.994,-84.9987 -49.9909,0 -100.994,28.0022 -100.994,84.9987l-86.0111 0c2.00449,-109.984 93.0167,-164.996 187.005,-164.996 94.0088,0 187.005,55.0122 187.005,164.996l0 295.005 0 118.994 0 10.0022c0,110.004 -92.9964,164.996 -187.005,164.996 -97.9975,0 -192.998,-54.992 -197.007,-164.996zm298.002 -10.0022l0 -118.994c0,-56.9965 -50.983,-84.9987 -100.994,-84.9987 -56.0044,0 -110.996,28.0022 -110.996,84.9987l0 128.996c0,56.9965 54.992,84.9987 110.996,84.9987 50.0111,0 100.994,-28.0022 100.994,-84.9987l0 -10.0022z"
        />
        <glyph
          unicode="A"
          horiz-adv-x="464"
          d="M236.01 591.005l72.9927 -387.003 -162.001 0 75.0006 387.003 6.99218 55.9847 7.0158 -55.9847zm-71.0084 142.985l-144.993 -733.99 87.0007 0 24.0002 123.001 193.986 0 24.0002 -123.001 87.0007 0 -142.985 733.99 -128.009 0z"
        />
        <glyph
          unicode="B"
          horiz-adv-x="498"
          d="M366.003 245.01l0 -81.0006c0,-48.0004 -39.9924,-83.0085 -86.0086,-83.0085l-135.993 0 0 254.01 135.993 0c48.0004,0 86.0086,-39.0003 86.0086,-90.0007zm86.0086 0c0,55.9847 -34.016,106.985 -89.0086,132.993 48.9925,27.0002 78.9927,75.9927 78.9927,127.985l0 64.0163c0,88.985 -72.0006,163.986 -171.993,163.986l-126.001 0 -86.0086 0 0 -733.99 86.0086 0 135.993 0c100.017,0 172.017,75.0006 172.017,164.009l0 81.0006zm-96.0008 260.978c0,-50.9768 -38.0082,-89.9771 -86.0086,-89.9771l-126.001 0 0 236.978 126.001 0c45.9925,0 86.0086,-34.9845 86.0086,-82.9849l0 -64.0163z"
        />
        <glyph
          unicode="D"
          horiz-adv-x="502"
          d="M452.011 171.001l0 399.003c0,88.985 -72.0006,163.986 -172.017,163.986l-222.002 0 0 -733.99 222.002 0c102.001,0 172.017,80.0085 172.017,171.001zm-86.0086 0c0,-51.0004 -38.0082,-90.0007 -86.0086,-90.0007l-135.993 0 0 571.989 135.993 0c46.0161,0 86.0086,-34.9845 86.0086,-82.9849l0 -399.003z"
        />
        <glyph
          unicode="E"
          horiz-adv-x="465"
          d="M144.001 323.01l228.002 0 0 81.0006 -228.002 0 0 248.978 267.994 0 0 81.0006 -354.003 0 0 -733.99 354.003 0 0 81.0006 -267.994 0 0 242.01z"
        />
        <glyph
          unicode="G"
          horiz-adv-x="473"
          d="M254.01 263.01l78.0006 0 0 -108.001c0,-57.0004 -51.0004,-85.0164 -101.009,-85.0164 -50.0083,0 -101.009,28.016 -101.009,85.0164l0 423.995c0,57.0004 51.0004,84.9928 101.009,84.9928 50.0083,0 101.009,-27.9923 101.009,-84.9928l85.9849 0c-1.98427,109.985 -93.0007,165.001 -186.994,165.001 -93.9929,0 -186.994,-55.0162 -186.994,-165.001l0 -423.995c0,-110.009 93.0007,-165.001 186.994,-165.001 36.9924,0 72.9927,12.0001 101.009,36.0003l0 -26.0081 85.9849 0 0 344.011 -163.986 0 0 -81.0006z"
        />
        <glyph
          unicode="H"
          horiz-adv-x="494"
          d="M57.9926 733.99l0 -733.99 86.0086 0 0 323.01 201.994 0 0 -323.01 86.0086 0 0 733.99 -86.0086 0 0 -329.979 -201.994 0 0 329.979 -86.0086 0z"
        />
        <glyph
          unicode="I"
          horiz-adv-x="206"
          d="M57.9926 733.99l0 -733.99 86.0086 0 0 733.99 -86.0086 0z"
        />
        <glyph
          unicode="J"
          horiz-adv-x="450"
          d="M104.009 155.009l-86.0086 0c0,-110.009 93.0007,-165.001 186.994,-165.001 94.0165,0 187.017,54.9926 187.017,165.001l0 578.981 -294.002 0 0 -81.0006 207.994 0 0 -497.98c0,-57.0004 -51.0004,-85.0164 -101.009,-85.0164 -49.9846,0 -100.985,28.016 -100.985,85.0164z"
        />
        <glyph
          unicode="K"
          horiz-adv-x="477"
          d="M144.001 468.004l0 265.986 -86.0086 0 0 -733.99 86.0086 0 0 309.002 6.99218 9.9922 197.009 -318.995 111.001 0 -255.002 396.003 231.994 337.987 -99.9929 0 -174.994 -265.986 -17.008 -29.0081 0 29.0081z"
        />
        <glyph
          unicode="L"
          horiz-adv-x="443"
          d="M57.9926 733.99l0 -733.99 354.003 0 0 81.0006 -267.994 0 0 652.989 -86.0086 0z"
        />
        <glyph
          unicode="M"
          horiz-adv-x="573"
          d="M246.002 255.994l77.0085 0 79.9849 209.01 23.0081 67.9848 0 -532.988 85.9849 0 0 733.99 -95.9771 0 -131.009 -375.995 -131.009 375.995 -96.0008 0 0 -733.99 86.0086 0 0 532.988 21.9923 -64.9848 80.0085 -212.01z"
        />
        <glyph
          unicode="N"
          horiz-adv-x="494"
          d="M324.003 287.01l-189.994 446.98 -76.0163 0 0 -733.99 86.0086 0 0 541.989 21.0002 -92.9771 191.009 -449.011 75.9927 0 0 733.99 -86.0086 0 0 -541.989 -21.9923 95.0086z"
        />
        <glyph
          unicode="O"
          horiz-adv-x="471"
          d="M44.0082 579.005l0 -423.995c0,-110.009 93.0007,-165.001 186.994,-165.001 93.9929,0 183.994,54.9926 186.994,165.001l0 423.995c-1.98427,109.985 -93.0007,165.001 -186.994,165.001 -93.9929,0 -186.994,-55.0162 -186.994,-165.001zm288.002 0l0 -423.995c0,-57.0004 -51.0004,-85.0164 -101.009,-85.0164 -50.0083,0 -101.009,28.016 -101.009,85.0164l0 423.995c0,57.0004 51.0004,84.9928 101.009,84.9928 50.0083,0 101.009,-27.9923 101.009,-84.9928z"
        />
        <glyph
          unicode="P"
          horiz-adv-x="492"
          d="M452.011 489.004l0 81.0006c0,88.985 -72.0006,163.986 -172.017,163.986l-222.002 0 0 -733.99 86.0086 0 0 318.003 135.993 0c102.001,0 172.017,80.0085 172.017,171.001zm-86.0086 0c0,-51.0004 -38.0082,-90.0007 -86.0086,-90.0007l-135.993 0 0 253.986 135.993 0c46.0161,0 86.0086,-34.9845 86.0086,-82.9849l0 -81.0006z"
        />
        <glyph
          unicode="R"
          horiz-adv-x="502"
          d="M366.003 489.004c0,-51.0004 -38.0082,-90.0007 -86.0086,-90.0007l-135.993 0 0 253.986 135.993 0c46.0161,0 86.0086,-34.9845 86.0086,-82.9849l0 -81.0006zm-27.0002 -161.009c69.0005,24.0002 113.009,89.0086 113.009,161.009l0 81.0006c0,88.985 -72.0006,163.986 -172.017,163.986l-222.002 0 0 -733.99 86.0086 0 0 318.003 108.993 0 110.009 -318.003 101.009 0 -125.009 327.995z"
        />
        <glyph
          unicode="S"
          horiz-adv-x="452"
          d="M119.009 155.009l-86.0086 0c2.00789,-111.001 89.0086,-163.017 179.009,-163.017 96.9929,0 196.986,60.0005 196.986,174.001 0,179.009 -282.994,262.018 -282.994,399.003 0,63.0005 53.0083,98.0086 102.001,98.0086 42.9925,0 83.0085,-27.0002 83.0085,-84.0007l85.9849 0c-1.98427,111.001 -84.0007,162.993 -168.993,162.993 -92.0086,0 -186.994,-60.9926 -186.994,-174.001 0,-184.986 282.994,-267.994 282.994,-399.003 0,-63.0005 -59.0083,-97.985 -113.009,-97.985 -48.0004,0 -91.985,27.0002 -91.985,84.0007z"
        />
        <glyph
          unicode="T"
          horiz-adv-x="453"
          d="M23.0081 652.989l153.993 0 0 -652.989 86.0086 0 0 652.989 153.993 0 0 81.0006 -153.993 0 -86.0086 0 -153.993 0 0 -81.0006z"
        />
        <glyph
          unicode="U"
          horiz-adv-x="486"
          d="M138.993 733.99l-85.9849 0 0 -578.981c0,-110.009 93.0007,-165.001 186.994,-165.001 93.9929,0 183.994,54.9926 186.994,165.001l0 578.981 -85.9849 0 0 -578.981c0,-57.0004 -51.0004,-85.0164 -101.009,-85.0164 -50.0083,0 -101.009,28.016 -101.009,85.0164l0 578.981z"
        />
        <glyph
          unicode="V"
          horiz-adv-x="479"
          d="M228.002 143.009l-113.009 590.981 -87.0007 0 143.009 -733.99 128.009 0 144.993 733.99 -87.0007 0 -114.993 -590.981 -7.0158 -56.0083 -6.99218 56.0083z"
        />
      </font>
      <clipPath id="id0">
        <path d="M666.59 5182.22l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id1">
        <path d="M2229.26 5176.99l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id2">
        <path d="M3776.35 5174.39l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id3">
        <path d="M2997.44 2973.15l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id4">
        <path d="M4546.81 2975.76l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id5">
        <path d="M5638.93 5173.71l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id6">
        <path d="M7195.25 5178.92l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id7">
        <path d="M8728.99 5165.87l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id8">
        <path d="M2997.16 3936.7l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id9">
        <path d="M4542.56 3931.49l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id10">
        <path d="M6094.77 3931.48l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id11">
        <path d="M3007.85 1970.54l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
      <clipPath id="id12">
        <path d="M4551.45 1967.94l1414.73 0 0 766.2 -1414.73 0 0 -766.2z" />
      </clipPath>
    </defs>
    <g id="Layer_x0020_1">
      <metadata id="CorelCorpID_0Corel-Layer" />
      <g id="_1535351824" style="filter: url(#shadow)">
        <g style="filter: url(#shadow)"></g>
      </g>
      <rect
        class="fil0 str0"
        x="763.76"
        y="5263.7"
        width="1182.36"
        height="533.83"
      />
      <text x="873.05" y="5177.94" class="fil1 fnt0">BASELINE RPJMD</text>
      <g id="_1535351680">
        <g style="clip-path: url(#id1)"></g>
      </g>
      <rect
        class="fil2 str0"
        x="2315.84"
        y="5263.7"
        width="1182.36"
        height="533.83"
      />
      <g id="_1535349760">
        <g style="clip-path: url(#id2)"></g>
      </g>
      <rect
        class="fil3 str0"
        x="3862.72"
        y="5263.7"
        width="1182.36"
        height="533.83"
      />
      <g id="_1535347024">
        <g style="clip-path: url(#id3)"></g>
      </g>
      <rect
        class="fil4 str0"
        x="3091.88"
        y="3068.39"
        width="1182.36"
        height="533.83"
      />
      <g id="_1535338912">
        <g style="clip-path: url(#id4)"></g>
      </g>
      <rect
        class="fil3 str0"
        x="4646.73"
        y="3070.91"
        width="1182.36"
        height="533.83"
        style="filter: url(#shadow)"
      />
      <text x="2488.71" y="6029.51" class="fil1 fnt0">STATUS AKHIR</text>
      <text x="4131.45" y="5177.94" class="fil1 fnt0">SISA RPJMD</text>
      <path
        class="fil5 str1"
        d="M5312.02 5708.94l179.69 0 0 -89.84 89.84 0 0 -179.69 -89.84 0 0 -89.84 -179.69 0 0 89.84 -89.84 0 0 179.69 89.84 0 0 89.84zm89.84 0m89.85 -44.92m44.92 -44.92m44.92 -89.85m-44.92 -89.84m-44.92 -44.92m-89.85 -44.92m-89.84 44.92m-44.92 44.92m-44.92 89.84m44.92 89.85m44.92 44.92"
      />
      <g id="_1535333776">
        <g>
          <path
            class="fil6"
            d="M2255.08 5534.09l-308.96 0 0 -6.94 308.96 0 0 6.94zm-5.77 -40.07l66.53 36.59 -66.53 36.59 0 -73.19z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            class="fil6"
            d="M2134.45 3335.3l0 2195.31 -6.94 0 0 -2195.31 3.47 -3.47 3.47 3.47zm-6.94 0l0 -3.47 3.47 0 -3.47 3.47zm903.61 3.47l-900.14 0 0 -6.94 900.14 0 0 6.94zm-5.77 -40.07l66.53 36.59 -66.53 36.59 0 -73.19z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            class="fil6"
            d="M3801.96 5534.09l-303.76 0 0 -6.94 303.76 0 0 6.94zm-5.77 -40.07l66.53 36.59 -66.53 36.59 0 -73.19z"
          />
        </g>
      </g>
      <g>
        <g style="clip-path: url(#id5)"></g>
      </g>
      <rect
        class="fil0 str0"
        x="5727.31"
        y="5263.7"
        width="1182.36"
        height="533.83"
      />
      <g>
        <g style="clip-path: url(#id6)"></g>
      </g>
      <rect
        class="fil2 str0"
        x="7279.39"
        y="5263.7"
        width="1182.36"
        height="533.83"
      />
      <g>
        <g style="clip-path: url(#id7)"></g>
      </g>
      <rect
        class="fil3 str0"
        x="8826.27"
        y="5263.7"
        width="1182.36"
        height="533.83"
        style="filter: url(#shadow)"
      />
      <g>
        <g style="clip-path: url(#id8)"></g>
      </g>
      <rect
        class="fil7 str0"
        x="3090.74"
        y="4029.32"
        width="1182.36"
        height="533.83"
      />
      <g>
        <g style="clip-path: url(#id9)"></g>
      </g>
      <rect
        class="fil3 str0"
        x="4642.83"
        y="4029.32"
        width="1182.36"
        height="533.83"
      />
      <g>
        <g style="clip-path: url(#id10)"></g>
      </g>
      <rect
        class="fil4 str0"
        x="6189.7"
        y="4029.32"
        width="1182.36"
        height="533.83"
      />
      <g>
        <g>
          <path
            class="fil6"
            d="M3685.31 4623.92l-1.37 906.7 -6.94 -0.01 1.37 -906.7 6.94 0.01zm-40.08 5.71l36.7 -66.47 36.5 66.58 -73.19 -0.11z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            class="fil6"
            d="M4450.43 6346.13l0 -548.6 6.94 0 0 548.6 -3.47 3.47 -3.47 -3.47zm3.47 3.47l-3.47 0 0 -3.47 3.47 3.47zm4963.55 0l-4963.55 0 0 -6.94 4963.55 0 3.47 3.47 -3.47 3.47zm3.47 -3.47l0 3.47 -3.47 0 3.47 -3.47zm0 -487.84l0 487.84 -6.94 0 0 -487.84 6.94 0zm-40.07 5.77l36.59 -66.53 36.59 66.53 -73.19 0z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            class="fil6"
            d="M7218.63 5534.09l-308.96 0 0 -6.94 308.96 0 0 6.94zm-5.77 -40.07l66.53 36.59 -66.53 36.59 0 -73.19z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            class="fil6"
            d="M8765.51 5534.09l-303.76 0 0 -6.94 303.76 0 0 6.94zm-5.77 -40.07l66.53 36.59 -66.53 36.59 0 -73.19z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            class="fil6"
            d="M7098 4878.11l0 652.5 -6.94 0 0 -652.5 3.47 -3.47 3.47 3.47zm-3.47 -3.47l3.47 0 0 3.47 -3.47 -3.47zm-313.65 0l313.65 0 0 6.94 -313.65 0 -3.47 -3.47 3.47 -3.47zm0 6.94l-3.47 0 0 -3.47 3.47 3.47zm3.47 -257.67l0 254.2 -6.94 0 0 -254.2 6.94 0zm-40.07 5.77l36.59 -66.53 36.59 66.53 -73.19 0z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            class="fil6"
            d="M4582.07 4299.71l-308.96 0 0 -6.94 308.96 0 0 6.94zm-5.77 -40.07l66.53 36.59 -66.53 36.59 0 -73.19z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            class="fil6"
            d="M5885.94 4292.76l303.76 0 0 6.94 -303.76 0 0 -6.94zm5.77 40.07l-66.53 -36.59 66.53 -36.59 0 73.19z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            class="fil6"
            d="M5240.83 3665.53l-3.35 363.83 -6.94 -0.06 3.35 -363.83 6.94 0.06zm-40.12 5.4l37.2 -66.19 35.98 66.86 -73.19 -0.67z"
          />
        </g>
      </g>
      <text x="2286.19" y="4273.04" class="fil1 fnt0">INTERVENSI</text>
      <text x="2461.52" y="4439.71" class="fil1 fnt0">VERIVALI</text>
      <text x="4769.89" y="4768.14" class="fil1 fnt0">TOTAL VERIVALI</text>
      <text x="5915.73" y="3240.81" class="fil1 fnt0">TOTAL</text>
      <text x="5915.73" y="3407.47" class="fil1 fnt0">PENANGANAN</text>
      <text x="5915.73" y="3574.14" class="fil1 fnt0">DTKS</text>
      <g>
        <g style="clip-path: url(#id11)"></g>
      </g>
      <rect
        class="fil4 str0"
        x="3097.09"
        y="2063.18"
        width="1182.36"
        height="533.83"
      />
      <g>
        <g style="clip-path: url(#id12)"></g>
      </g>
      <rect
        class="fil3 str0"
        x="4646.73"
        y="2064.37"
        width="1182.36"
        height="533.83"
        style="filter: url(#shadow)"
      />
      <text x="7471.86" y="4273.04" class="fil1 fnt0">INTERVENSI</text>
      <text x="7471.86" y="4439.71" class="fil1 fnt0">VERIVALI</text>
      <text x="2189.42" y="3058.5" class="fil1 fnt0">PENANGANAN</text>
      <text x="2606.43" y="3225.17" class="fil1 fnt0">RPJMD</text>
      <text x="5691.35" y="6029.51" class="fil1 fnt0">PERTUMBUHAN BARU</text>
      <text x="7389.08" y="6029.51" class="fil1 fnt0">SISA RTLH BARU</text>
      <text x="8909.33" y="5177.94" class="fil1 fnt0">TOTAL SISA RTLH</text>
      <text x="2189.42" y="2320.22" class="fil1 fnt0">PENANGANAN</text>
      <text x="2359.09" y="2486.88" class="fil1 fnt0">NON-DTKS</text>
      <g>
        <g>
          <path
            class="fil6"
            d="M4585.95 3340.89l-311.74 -2.11 0.05 -6.94 311.74 2.11 -0.05 6.94zm-5.5 -40.11l66.28 37.05 -66.78 36.15 0.5 -73.19z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            class="fil6"
            d="M4585.96 2334.56l-306.53 -1 0.02 -6.94 306.53 1 -0.02 6.94zm-5.64 -40.09l66.41 36.81 -66.65 36.38 0.24 -73.19z"
          />
        </g>
      </g>
      <g>
        <g>
          <path
            class="fil6"
            d="M5241.39 2658.96l0 411.95 -6.94 0 0 -411.95 6.94 0zm-40.07 5.77l36.59 -66.53 36.59 66.53 -73.19 0z"
          />
        </g>
      </g>
      <text x="5936.56" y="2244.7" class="fil1 fnt0">TOTAL</text>
      <text x="5936.56" y="2411.37" class="fil1 fnt0">PENANGANAN</text>
      <text x="5936.56" y="2578.03" class="fil1 fnt0">DTKS + NON DTKS</text>
      <text x="8071.91" y="2510.33" class="fil1 fnt1">SIMPERUM</text>
      <text x="8401.49" y="2711.41" class="fil1 fnt0">
        SNAPSHOT: 30 JUNI 2023
      </text>
      <text x="1029.93" y="5601.98" class="fil8 fnt2">1.682.723</text>
      <text x="2582.01" y="5601.98" class="fil8 fnt2">1.582.024</text>
      <text x="4221.69" y="5601.98" class="fil8 fnt2">529.508</text>
      <text x="6053.75" y="5601.98" class="fil8 fnt2">977.158</text>
      <text x="7605.83" y="5601.98" class="fil8 fnt2">482.954</text>
      <text x="9123.16" y="5601.98" class="fil8 fnt2">1.012.462</text>
      <text x="3349.71" y="4367.6" class="fil8 fnt2">1.052.516</text>
      <text x="4901.8" y="4367.6" class="fil8 fnt2">1.546.720</text>
      <text x="6510.76" y="4367.6" class="fil8 fnt2">494.204</text>
      <text x="3407.73" y="3406.66" class="fil8 fnt2">100.699</text>
      <text x="4931.57" y="3409.19" class="fil8 fnt2">1.647.419</text>
      <text x="3432.38" y="2401.45" class="fil8 fnt2">55.854</text>
      <text x="4905.7" y="2402.65" class="fil8 fnt2">1.703.273</text>
    </g>
  </svg>
</template>
<script>
export default {
  methods: {
    downloadSVGAsPNG(e) {
      const canvas = document.createElement('canvas')
      const svg = document.querySelector('svg')
      const base64doc = btoa(unescape(encodeURIComponent(svg.outerHTML)))
      const w = parseInt(svg.getAttribute('width'))
      const h = parseInt(svg.getAttribute('height'))
      const img_to_download = document.createElement('img')
      img_to_download.src = 'data:image/svg+xml;base64,' + base64doc
      img_to_download.onload = function () {
        canvas.setAttribute('width', w)
        canvas.setAttribute('height', h)
        const context = canvas.getContext('2d')
        //context.clearRect(0, 0, w, h);
        context.drawImage(img_to_download, 0, 0, w, h)
        const dataURL = canvas.toDataURL('image/png')
        if (window.navigator.msSaveBlob) {
          window.navigator.msSaveBlob(canvas.msToBlob(), 'download.png')
          e.preventDefault()
        } else {
          const a = document.createElement('a')
          const my_evt = new MouseEvent('click')
          a.download = 'download.png'
          a.href = dataURL
          a.dispatchEvent(my_evt)
        }
        //canvas.parentNode.removeChild(canvas);
      }
    },
  },
}
</script>
<style type="text/css">
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700&family=PT+Sans+Narrow&family=Quicksand:wght@400;700&display=swap');
@font-face {
  font-family: 'PT Sans Narrow';
  font-variant: normal;
  font-style: normal;
  font-weight: normal;
  src: url('#FontID0') format(svg);
}
.str0 {
  stroke: #fefefe;
  stroke-width: 55.56;
  stroke-miterlimit: 2.61313;
}
.str1 {
  stroke: #2b2a29;
  stroke-width: 6.94;
  stroke-miterlimit: 2.61313;
}
.fil5 {
  fill: none;
  fill-rule: nonzero;
}
.fil8 {
  fill: #fefefe;
}
.fil1 {
  fill: #2b2a29;
}
.fil4 {
  fill: #276db9;
}
.fil7 {
  fill: #563b8d;
}
.fil3 {
  fill: #b02878;
}
.fil0 {
  fill: #f26515;
}
.fil2 {
  fill: #fe9f1b;
}
.fil6 {
  fill: #2b2a29;
  fill-rule: nonzero;
}
.fnt0 {
  font-weight: normal;
  font-size: 166.67px;
  font-family: 'PT Sans Narrow';
}
.fnt2 {
  font-weight: normal;
  font-size: 194.44px;
  font-family: 'PT Sans Narrow';
}
.fnt1 {
  font-weight: normal;
  font-size: 500px;
  font-family: 'PT Sans Narrow';
}
</style>
