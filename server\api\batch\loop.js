require('dotenv').config({path: require('path').resolve(__dirname, '../../.env')});
console.log(require('path').resolve(__dirname, '../../.env'))
const fs = require('fs');
const csv = require('csv-parse/sync');
const {stringify} = require('csv-stringify/sync');
const mysql = require('mysql');
const util = require('util');
var readlines = require("n-readlines");

// MySQL Configuration with improved timeout settings
const pool = mysql.createPool({
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '2'),
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '3307'),
  // acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
  // connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT || '60000'),
  // timeout: parseInt(process.env.DB_TIMEOUT || '60000'),
  waitForConnections: true,
  queueLimit: 0
});

pool.getConnection((err, connection) => {
  if (err) {
    if (err.code === "PROTOCOL_CONNECTION_LOST") {
      console.error("Database connection was closed.");
    }
    if (err.code === "ER_CON_COUNT_ERROR") {
      console.error("Database has too many connections.");
    }
    if (err.code === "ECONNREFUSED") {
      console.error("Database connection was refused.");
    }
  }
  if (connection) connection.release();
  return;
});
pool.query = util.promisify(pool.query); // Magic happens here.


// Process records in batches
async function run(row) {

  // if (row.Kabupaten < 'KOTA MAGELANG') return

  //console.log(row.Kabupaten)

  // const desil = [null,0,1,2,3,4,5,'6-10','NON']

  // for(let dd of desil) {
  console.log(row.Kabupaten, row.Sisa)
  // const records = await executeQuery(`update tmp_dtse td
  //     left join prm_pbdt pp 
  //     on td.NIKENC = pp.NIK
  //     set td.tipe_data = CASE WHEN COALESCE(pp.KepemilikanRumah,1) = 1 THEN 'rtlh' ELSE 'backlog' END
  //     where td.nmkab = '${row.Kabupaten}'`);    
  // }

  // await pool.query(`insert ignore into tmp_1022final
  //   select nik, AES_ENCRYPT(nik , '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5') NIKENC, td.nama, td.alamat, -4, 'rtlh', td.Intervensi, td.nmkab, td.nmkec, td.nmdesa, td.desil_dtsen from tmp_dtse td 
  //   where td.Intervensi IS NULL AND td.nmkab = '${row.Kabupaten}'
  //   `)

  
  await pool.query(`insert ignore into tmp_1022final
    select td.nik, AES_ENCRYPT(td.nik , '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5') NIKENC, td.nama, td.alamat, -4, 'rtlh', td.Intervensi, td.nmkab, td.nmkec, td.nmdesa, td.desil_dtsen from tmp_dtse td 
    left join tmp_1022final f
    on td.nik = f.NIK 
    where f.NIK IS NULL AND td.nmkab = '${row.Kabupaten}' AND td.nik > 3300000000000000
    LIMIT ${row.Sisa}
    `)

  // for (let r of records) {
  //   fs.appendFileSync('jml_intervensi.csv', `${r.Kabupaten},${r.SumberName},${r.Tahun},${r.Jml}\n`)
  // }
  return null;
}

// const readCSV = async () => {
//   const SRC_FILE = 'sisartlh1022113.csv'
//   console.log(`Reading CSV file: ${SRC_FILE}`);
//   // Read CSV file

//   var liner = new readlines(SRC_FILE);
//   let next = ''
//   let start = false
//   let i = 0
//   let vals = []
//   const sql = `INSERT IGNORE INTO tmp_1022 (NIK, Nama, Alamat, KodeDagri, TipeData, Intervensi, Tag1, Tag2, Kabupaten, Kecamatan, Kelurahan, NIKENC) VALUES `
//   // skip header
//   next = liner.next()
//   while ((next = liner.next())) {
//     i++
//     let line = next.toString("ascii").split(",").map(d => d.trim());
//     vals.push(`(${line[0] || '0'}, ${mysql.escape(line[1])}, ${mysql.escape(line[2])}, ${mysql.escape(line[3])}, ${mysql.escape(line[4])}, ${line[5] && line[6]?mysql.escape(line[5]+' '+line[6]):'NULL'}, ${mysql.escape(line[7])}, ${mysql.escape(line[8])}, ${mysql.escape(line[9])}, ${mysql.escape(line[10])}, ${mysql.escape(line[11])}, AES_ENCRYPT('${line[0] || 0}', '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5'))`)
    
//     if (i % 500 == 0) {
//       await pool.query(sql + vals.join(','))
//       vals = []
//       console.log(i)        
//     }
//     // if (i> 10) break
//   }
//   await pool.query(sql + vals.join(','))      
// }

async function main() {
  try {

    // await readCSV()

    // const records = await executeQuery(`select DISTINCT t.KodeDagri from tmp_1022 t `);
    const records = await pool.query(`select *, Jml - Terisi Sisa from tmp_dtserekap
      where Jml - Terisi > 0`);

    // const records = [{ Kabupaten: 'KLATEN' }]
    // console.log(records)
    for (const row of records) {
      await run(row)
    }

    console.log('processed successfully');

    // End MySQL pool
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(0);
    });

    process.exit(0)
  } catch (error) {
    console.error('Error:', error);

    // End MySQL pool on error
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(1);
    });
  }
}

// Handle process termination
// process.on('SIGINT', () => {
//   console.log('Process interrupted, closing connections...');
//   pool.end(err => {
//     if (err) console.error('Error closing MySQL pool:', err);
//     process.exit(2);
//   });
// });

main();