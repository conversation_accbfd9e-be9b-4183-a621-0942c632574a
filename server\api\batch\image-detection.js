const tf = require('@tensorflow/tfjs-node');
const cocoSsd = require('@tensorflow-models/coco-ssd');
const fs = require('fs');
const jpeg = require('jpeg-js');

async function detectObjects(imagePath) {
  const imageBuffer = fs.readFileSync(imagePath);
  const pixels = jpeg.decode(imageBuffer, true);
  const model = await cocoSsd.load();
  const predictions = await model.detect(tf.node.decodeImage(pixels.data, pixels.width, pixels.height, 3));
  console.log('Predictions:', predictions);
}

detectObjects('tmp/orang.jpg');
