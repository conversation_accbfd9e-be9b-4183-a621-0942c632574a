<template>
  <v-menu dense offset-y v-bind="$attrs">
    <template v-slot:activator="{ on }">
      <slot :on="on">
        <v-btn v-on="on" :color="color">{{ text }}</v-btn>
      </slot>
    </template>
    <v-list dense>
      <v-list-item
        v-for="(item, idx) in menu"
        :key="idx"
        @click="ItemClick(item)"
      >
        <v-list-item-content>
          <v-list-item-title>{{ item }}</v-list-item-title>
        </v-list-item-content>
      </v-list-item>
    </v-list>
  </v-menu>
</template>

<script>
export default {
  props: {
    menu: Array,
    text: {
      type: String,
      value: 'MENU',
    },
    color: {
      type: String,
      value: 'primary',
    },
    default: [],
  },
  methods: {
    ItemClick(txt) {
      this.$emit('item-click', txt)
    },
  },
}
</script>
