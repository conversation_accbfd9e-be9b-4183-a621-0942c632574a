<template>
  <div class="sidebar-validasi">
    <div style="padding: 10px; display: flex">
      <XSelect
        v-show="!searchMode"
        dbref="PRM.SelPBDTCity"
        :dbparams="{ nocache: true }"
        :value.sync="kabupaten"
        :valueAsObject="true"
      />
      <XInput
        type="text"
        v-show="searchMode"
        :value.sync="keyword"
        placeholder="Cari .."
      />
      <v-icon
        style="height: 27px; margin-left: 10px"
        v-show="!searchMode"
        @click="searchMode = !searchMode"
        >search</v-icon
      >
      <v-icon
        style="height: 27px; margin-left: 10px"
        v-show="searchMode"
        @click="searchMode = !searchMode"
        >clear</v-icon
      >
    </div>
    <div>
      <List
        dbref="PRM.SelValidasiArea"
        :dbparams="{
          Kabupaten: kabupaten.txt,
        }"
        :filters="{
          keyword: keyword,
          filter: filterArea,
        }"
        height="calc(100vh - 160px)"
        @itemClick="AreaClicked"
      >
        <template v-slot="{ row }">
          <div :class="`ordr-${row.Ordr}`">
            <div
              :style="{
                color: row.KelTipe == 'KEL' ? 'rgb(30, 136, 229)' : '#333',
              }"
            >
              <v-icon
                v-show="row.Ordr == 2"
                style="height: 16px"
                :style="{ color: warnaDesa[row.Priority] || 'palegreen' }"
              >
                mdi-circle-medium
              </v-icon>
              {{ row.KelTipe == 'KEL' ? 'K.' : '' }}
              {{ row.AreaName }}
              <span
                v-if="row.DataTag"
                style="
                  font-size: 10px;
                  background: lightblue;
                  padding: 3px;
                  border-radius: 3px;
                "
              >
                {{ row.DataTag }}
              </span>
            </div>

            <div class="status">
              <div
                class="badge s-jml"
                v-tooltip="
                  `Validasi: ${row.SudahValidasi} / Total: ${row.Total}`
                "
              >
                {{ row.SudahValidasi }} / {{ row.Total }}
              </div>
            </div>
          </div>
        </template>
      </List>
    </div>
  </div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  data: () => ({
    kabupaten: { val: null, txt: null },
    selectedArea: {},
    searchMode: false,
    keyword: '',
    warnaDesa: ['palegreen', 'red', 'yellow', 'palegreen'],
  }),
  props: {
    value: Object,
  },
  watch: {
    searchMode(val) {
      if (!val) this.keyword = ''
    },
    value(val) {
      if (val.KodeDagri && val.KodeDagri != this.kodeDagri)
        this.getByKodeDagri(val.KodeDagri)
      else {
        if (val.Kabupaten) this.kabupaten = { txt: val.Kabupaten }
      }
    },
  },
  mounted() {
    if (Object.keys(this.$route.query).length) {
      this.area = this.$route.query
      if (this.area.KodeDagri) {
        this.kabupaten = {val: (this.area.KodeDagri+'').substr(0,4), txt: this.area.Kabupaten}
        this.AreaClicked(this.area)
      }
    } else if (sessionStorage.getItem('side-area')) {
      setTimeout(() => {
        let areaVal = JSON.parse(sessionStorage.getItem('side-area'))
        this.kabupaten = { val: areaVal.KabupatenID, txt: areaVal.Kabupaten }
        // this.kabupaten = areaVal.KabupatenID
        this.$emit('update:value', areaVal)
      }, 500)
    }
  },
  methods: {
    ...mapActions(['setPageFocused']),
    AreaClicked(item) {
      if (!item || (!item.AreaID && !item.Kelurahan)) return
      this.setPageFocused(true)
      let areaVal = {
        Kabupaten: this.kabupaten.txt,
        KabupatenID: this.kabupaten.val,
        Kecamatan: item.Kecamatan,
        Kelurahan: item.AreaName || item.Kelurahan,
        KelurahanID: item.AreaID,
        KodeDagri: item.KodeDagri,
      }
      sessionStorage.setItem('side-area', JSON.stringify(areaVal))
      this.$emit('update:value', areaVal)
    },
    async getByKodeDagri(kodeDagri) {
      if (this.kodeDagri == kodeDagri) return
      this.kodeDagri = kodeDagri
      let res = await this.$api.call('Arch.SelFullArea', {
        KodeDagri: kodeDagri,
      })
      if (res.success) {
        let item = res.data[0]
        let areaVal = {
          ...item,
          tabId: this.tabId,
          Sumber: this.tabId,
          Tahun: this.proposal.InputName,
          ProposalID: this.proposal.ProposalID,
          KodeDagri: kodeDagri,
        }
        window.sessionStorage.setItem('side-area', JSON.stringify(areaVal))
        this.$emit('update:value', areaVal)
      }
    },
    filterArea(item) {
      return item.AreaName.match(new RegExp(this.keyword, 'i'))
    },
  },
}
</script>
<style lang="scss">
.sidebar-validasi {
  background: white;
  .ui-list {
    .--item {
      border-bottom: 1px solid #ddd;
      padding: 5px 8px;
      font-size: 12px;
      .ordr-1 {
        font-weight: bold;
        .status {
          display: none;
        }
      }
      .ordr-2 {
        padding-left: 10px;
        display: flex;
        & > div {
          flex: 1;
        }
        & > div:first-child {
          flex: 2;
        }
        .status {
          justify-content: flex-end;
          display: flex;

          .badge {
            padding-right: 9px;
            padding-left: 9px;
            border-radius: 9px;
          }

          .s-jml {
            background-color: #f3f3f3;
          }
        }
      }
    }
  }
}
</style>
