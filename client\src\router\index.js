import Vue from 'vue'
import VueRouter from 'vue-router'
//import store from "../store/modules/common";
import Home from '../pages/Dashboard/Home.vue'
import Public from '../pages/App/Public/PublicView.vue'
import PetaRTLH from '../pages/App/Public/Map.vue'
import Login from '../pages/App/Login.vue'
import Logout from '../pages/App/Logout.vue'
import AdminRoutes from './administrasi'
import BacklogRoutes from './backlog'
import RelokasiRoutes from './relokasi'
import RTLHRoutes from './rtlh'
import KebencanaanRoutes from './kebencanaan'
import IntegrationRoutes from './integration'

Vue.use(VueRouter)

const routes = [
  ...AdminRoutes,
  ...KebencanaanRoutes,
  ...RTLHRoutes,
  ...BacklogRoutes,
  ...RelokasiRoutes,
  ...IntegrationRoutes,
  {
    path: '/',
    component: Public,
    meta: { title: 'SIMPERUM', noauth: true },
  },
  {
    path: '/Main/App/Home',
    name: 'Home',
    component: Home,
  },
  {
    path: '/doc-view/:id',
    name: 'DocView',
    component: () => import('../pages/App/Public/Verification.vue'),
    meta: { title: 'SIMPERUM', noauth: true },
  },
  {
    path: '/public',
    name: 'Public',
    component: Public,
    meta: { title: 'SIMPERUM', noauth: true },
  },
  {
    path: '/peta-rtlh',
    name: 'PetaRTLH',
    component: PetaRTLH,
    meta: { title: 'SIMPERUM', noauth: true },
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: { title: 'SIMPERUM', noauth: true },
  },
  {
    path: '/Main/App/Logout',
    name: 'Logout',
    component: Logout,
    meta: { noauth: true },
  },
  {
    path: '/register-kkn',
    name: 'RegisterKKN',
    component: () => import('../pages/App/Public/RegisterKKN.vue'),
    meta: { title: 'Register KKN - SIMPERUM', noauth: true },
  },
]

const router = new VueRouter({
  mode: 'history',
  // base: process.env.BASE_URL,
  base: '/',
  routes,
})

router.beforeEach((to, from, next) => {
  window._rwx = ''
  if (to.meta.noauth) return next()
  else {
    if (localStorage.getItem('menu-access')) {
      // next()
      let menu = JSON.parse(localStorage.getItem('menu-access'))
      if (to.path === '/') next()
      else {
        // let pages = store.state.user.pages
        // if (pages[to.path] && pages[to.path].access) {
        //   store.state.pageId = pages[to.path].id
        //   return next()
        // } else {
        //   return next({ path: '/notfound' })
        // }
        if (menu[to.path]) {
          sessionStorage.setItem('coms-access', JSON.stringify(menu[to.path]))
          window._rwx = menu[to.path] === true ? 'rwx' : menu[to.path]
          return next()
        } else {
          let found = false
          for (let m in menu) {
            if (to.path.match(m)) {
              found = true
              window._rwx = menu[m] === true ? 'rwx' : menu[m]
              break
            }
          }
          if (found) {
            return next()
          } else if (to.path == '/Main/App/Home') {
            return next({ path: '/' })
          } else {
            return next({ path: '/Main/App/Home' })
          }
        }
      }
    } else {
      return next({ path: '/login' })
    }
  }
})
const DEFAULT_TITLE = 'SIMPERUM'
router.afterEach((to) => {
  // Use next tick to handle router history correctly
  // see: https://github.com/vuejs/vue-router/issues/914#issuecomment-384477609
  Vue.nextTick(() => {
    document.title = to.meta.title || DEFAULT_TITLE
  })
})

export default router
