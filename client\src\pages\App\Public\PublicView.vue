<template>
  <div class="app-container">
    <header>
      <div class="logo">
        <img src="/imgs/simperum.png" alt="Simperum Logo" class="logo-img" />
        <span>Sim</span><span class="highlight">perum.</span>
      </div>

      <!-- <nav>
        <RouterLink to="/" class="active">Home</RouterLink>
        <RouterLink to="/shop">Shop</RouterLink>
        <RouterLink to="/collection">Collection</RouterLink>
        <RouterLink to="/gallery">Gallery</RouterLink>
      </nav> -->
      <div class="grow" />

      <ButtonLogin />
    </header>
    <main>
      <div class="hero-section" style="display: flex">
        <div class="hero-content">
          <div class="hero-number">Aplikasi</div>
          <h1 class="hero-title">Simperum<span class="dot">.</span></h1>
          <!-- <p class="hero-subtitle">Pintu Gerbang Satu Data Perumahan Jawa Tengah</p> -->

          <!-- <div class="cta-button">
          <span class="arrow-icon">→</span>
          <span>Masuk</span>
        </div> -->

          <div class="tutorial-card">
            <div class="play-button">
              <img
                src="/imgs/logo.png"
                alt="Play Icon"
                class="play-icon"
                style="width: 45px;"
              />
            </div>
            <span class="tutorial-text">Disperakim<br />Provinsi Jawa Tengah</span>
          </div>
        </div>

        <div class="hero-image">
          <!-- Image is shown as background in CSS -->
        </div>

        <!-- <div class="slider-controls">
        <button class="prev-button">←</button>
        <div class="slider-dots">
          <span class="dot active"></span>
          <span class="dot"></span>
          <span class="dot"></span>
          <span class="dot"></span>
        </div>
        <button class="next-button">→</button>
      </div>

      <div class="scroll-indicator">
        <span>Scroll</span>
        <span class="scroll-arrow">↓</span>
      </div> -->
      </div>
      <div class="partners-section">
        <h2 class="partners-title">Dokumentasi</h2>
        <br />
        <v-carousel hide-delimiters cycle>
          <v-carousel-item>
            <div class="video-container">
              <iframe
                src="https://www.youtube.com/embed/3XIJ54Huo_A"
                title="YouTube video player"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen="allowfullscreen"
              />
            </div>
          </v-carousel-item>
          <v-carousel-item>
            <div class="video-container">
              <iframe
                src="https://www.youtube.com/embed/uDy52H9racI"
                title="YouTube video player"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
              ></iframe>
            </div>
          </v-carousel-item>
          <v-carousel-item>
            <div class="video-container">
              <iframe
            src="https://www.youtube.com/embed/0862kvTjYqk"
                title="YouTube video player"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
              ></iframe>
            </div>
          </v-carousel-item>
          <v-carousel-item>
            <div class="video-container">
              <iframe
            src="https://www.youtube.com/embed/9av7lYR319k"
                title="YouTube video player"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
              ></iframe>
            </div>
          </v-carousel-item>
          <v-carousel-item>
            <div class="video-container">
              <iframe
            src="https://www.youtube.com/embed/NFcUHpjKW3k"
                title="YouTube video player"
                frameborder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowfullscreen
              ></iframe>
            </div>
          </v-carousel-item>
        </v-carousel>
      </div>

      <!-- Partners Section -->
      <div class="partners-section">
        <h2 class="partners-title">Mitra Aplikasi</h2>
        <div class="partners-container">
          <div class="partner-logo">
            <img src="/imgs/igahp.svg" alt="IGAHP Logo" />
          </div>
          <div class="partner-logo">
            <img src="/imgs/caribdt-min.png" alt="Caribdt Logo" />
          </div>
          <!-- <div class="partner-logo">
            <img src="/imgs/partners/ertlh.png" alt="ERTLH Logo" />
          </div> -->
        </div>
      </div>
    </main>
    <footer>
      <div class="footer-content">
        <div class="footer-logo-section">
          <img src="/imgs/logo.png" alt="Logo" class="footer-logo-img" />
          <div class="footer-info">
            <h3>Disperakim Provinsi Jawa Tengah</h3>
            <p>Jl. Madukoro Blok AA-BB, Semarang, Jawa Tengah</p>
            <p>Email: <EMAIL></p>
            <p>Telp: (024) 7608201</p>
          </div>
        </div>
      </div>
      <div class="footer-bottom">
        <p>&copy; 2025 Simperum. All rights reserved.</p>
      </div>
    </footer>
  </div>
</template>

<script>
import ButtonLogin from './ButtonLogin.vue'
// No imports needed for this simplified version
import { mapActions } from 'vuex'
export default {
  components: {
    ButtonLogin
  },
  data: () => ({
    doCycle: true,
    slideIndex: 0,
  }),
  computed: {},
  mounted() {
    if (localStorage.getItem('user')) {
      this.$router.push({ name: 'Home' })
    } else if (this.isMobile) {
      this.$router.push({ name: 'Login' })
    } else {
      setTimeout(() => {
        this.doCycle = false
      }, 12000)
    }
  },
  methods: {
    ...mapActions(['setMenu', 'setUser']),
    async StopCycle() {
      this.doCycle = false
    },
  },
}

</script>

<style scoped>
.app-container {
  max-width: 100%;
  margin: 0;
  padding: 0;
  height: 100vh;
  overflow: auto;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  max-width: 1280px;
  margin: 0 auto;
}

.logo {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1.8rem;
  font-weight: 300;
  color: #888;
  letter-spacing: 0.5px;
}

.logo-img {
  height: 50px;
  width: auto;
}

.highlight {
  font-weight: 500;
  color: #333;
  margin-left: -8px;
}

nav {
  display: flex;
  gap: 2rem;
}

nav a {
  color: #888;
  text-decoration: none;
  font-size: 1rem;
  position: relative;
  padding: 0.5rem 0;
}

nav a:hover,
nav a.active {
  color: #333;
}

nav a.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 5px;
  height: 5px;
  background-color: #8bc34a;
  border-radius: 50%;
}

.profile {
  display: flex;
  align-items: center;
}

.profile-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f0f0f0;
  border: 1px solid #ddd;
}

@media (max-width: 768px) {
  header {
    /* flex-direction: column; */
    gap: 1rem;
  }

  nav {
    gap: 1rem;
  }
}

footer {
  background-color: #222;
  color: #f0f0f0;
  padding: 2rem 0 1rem;
  /* margin-top: 3rem; */
}

.footer-content {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 2rem;
}

.footer-logo-section {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
}

.footer-logo-img {
  width: 80px;
  height: auto;
}

.footer-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.footer-info h3 {
  font-size: 1.2rem;
  font-weight: 500;
  margin: 0 0 0.5rem 0;
  color: #fff;
}

.footer-info p {
  margin: 0;
  font-size: 0.9rem;
  color: #aaa;
}

.footer-bottom {
  border-top: 1px solid #333;
  padding-top: 1.5rem;
  text-align: center;
  font-size: 0.8rem;
  color: #777;
  max-width: 1280px;
  margin: 0 auto;
  padding: 1.5rem 2rem 0;
}

@media (max-width: 768px) {
  .footer-logo-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
}
main {
  width: 100%;
  padding: 0;
  margin: 0;
  background-color: #f2f2f2;
}

.hero-section {
  position: relative;
  /* display: flex; */
  /* min-height: calc(100vh - 80px); */
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
}

.hero-content {
  flex: 1.3;
  padding-top: 4rem;
  position: relative;
  z-index: 2;
}

.hero-number {
  font-size: 5rem;
  font-weight: 200;
  color: #ddd;
  margin-bottom: -2rem;
}

.hero-title {
  font-size: 7rem;
  font-weight: 700;
  color: #333;
  line-height: 1;
  margin-bottom: 1.5rem;
}

.dot {
  color: #8bc34a;
}

.hero-subtitle {
  font-size: 1.2rem;
  color: #666;
  /* max-width: 400px; */
  margin-bottom: 2.5rem;
}

.cta-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: #8bc34a;
  font-size: 1rem;
  cursor: pointer;
}

.arrow-icon {
  font-size: 1.2rem;
}

.tutorial-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 10px;
  width: 250px;
  margin-top: 3rem;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.play-button {
  width: 40px;
  height: 40px;
  /* background-color: #333; */
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-icon {
  color: white;
  font-size: 0.8rem;
}

.tutorial-text {
  color: #666;
  font-size: 0.9rem;
}

.hero-image {
  flex: 1;
  background-image: url('/imgs/hero.png');
  /* background-image: url('https://da28rauy2a860.cloudfront.net/completehome/wp-content/uploads/2021/01/07115205/Hero-5.jpg'); */
  background-size: cover;
  background-position: center;
  border-radius: 10px;
  position: relative;
  height: 500px;
}

.slider-controls {
  position: absolute;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  align-items: center;
  gap: 1rem;
}

.prev-button,
.next-button {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #f8f9fa;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 1.2rem;
}

.next-button {
  background-color: #333;
  color: white;
}

.slider-dots {
  display: flex;
  gap: 0.5rem;
}

.slider-dots .dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #ddd;
}

.slider-dots .dot.active {
  background-color: #333;
}

.scroll-indicator {
  position: absolute;
  right: 2rem;
  bottom: 2rem;
  writing-mode: vertical-rl;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #666;
  font-size: 0.8rem;
  text-transform: uppercase;
  letter-spacing: 2px;
}

@media (max-width: 1024px) {
  .hero-section {
    flex-direction: column;
  }

  .hero-content {
    padding-top: 2rem;
    padding-bottom: 2rem;
  }

  .hero-title {
    font-size: 4rem;
  }

  .hero-image {
    height: 400px;
    margin-bottom: 4rem;
  }
}

/* Responsive video container */
.video-container {
  position: relative;
  width: 100%;
  padding-bottom: 56.25%; /* 16:9 aspect ratio (9/16 = 0.5625 or 56.25%) */
  height: 0;
  overflow: hidden;
  border-radius: 10px;
  box-shadow: rgba(0, 0, 0, 0.5) 0px 0px 20px;
  margin: auto;
}

.video-container iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 10px;
}

/* Additional responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .video-container {
    margin: 0 auto;
    width: 100%;
  }
  .tutorial-card {
    width: 100%;
  }
}

/* Partners Section Styles */
.partners-section {
  padding: 4rem 2rem;
  /* background-color: #f9f9f9; */
  text-align: center;
  max-width: 1280px;
  margin: 0 auto;
}

.partners-title {
  font-size: 2.5rem;
  color: #333;
  margin-bottom: 3rem;
  position: relative;
  display: inline-block;
}

.partners-title:after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background-color: #8bc34a;
}

.partners-container {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
  gap: 3rem;
}

.partner-logo {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 1.5rem;
  border-radius: 10px;
  transition:
    transform 0.3s ease,
    box-shadow 0.3s ease;
  width: 200px;
  height: 120px;
}

.partner-logo:hover {
  background-color: white;
  transform: translateY(-5px);
  /* box-shadow: 0 5px 15px rgba(0,0,0,0.05); */
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.partner-logo img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

@media (max-width: 768px) {
  .partners-section {
    padding: 3rem 1rem;
  }

  .partners-title {
    font-size: 2rem;
    margin-bottom: 2rem;
  }

  .partners-container {
    gap: 2rem;
  }

  .partner-logo {
    width: 150px;
    height: 100px;
    padding: 1rem;
  }
  .logo span {
    display:none
  }
}
@media (max-width: 768px) {
  .hero-number {
    font-size: 3rem;
    margin-bottom: -1rem;
  }

  .hero-title {
    font-size: 3rem;
  }
}
.v-carousel {
  height: auto !important;
}

.v-carousel__item{
  height: auto !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<style>

.v-carousel__item{
  height: auto !important;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
