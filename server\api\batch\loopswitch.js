require('dotenv').config({path: require('path').resolve(__dirname, '../../.env')});
console.log(require('path').resolve(__dirname, '../../.env'))
const fs = require('fs');
const csv = require('csv-parse/sync');
const {stringify} = require('csv-stringify/sync');
const mysql = require('mysql');
const util = require('util');
var readlines = require("n-readlines");

// MySQL Configuration with improved timeout settings
const pool = mysql.createPool({
  connectionLimit: parseInt(process.env.DB_CONNECTION_LIMIT || '20'),
  host: process.env.DB_HOST,
  user: process.env.DB_USER,
  password: process.env.DB_PASSWORD,
  database: process.env.DB_NAME,
  port: parseInt(process.env.DB_PORT || '3307'),
  // acquireTimeout: parseInt(process.env.DB_ACQUIRE_TIMEOUT || '60000'),
  // connectTimeout: parseInt(process.env.DB_CONNECT_TIMEOUT || '60000'),
  // timeout: parseInt(process.env.DB_TIMEOUT || '60000'),
  waitForConnections: true,
  queueLimit: 0
});

// Function to get a connection with retry logic
const getConnection = () => {
  return new Promise((resolve, reject) => {
    pool.getConnection((err, connection) => {
      if (err) {
        console.error('Error getting MySQL connection:', err);
        if (err.code === 'PROTOCOL_CONNECTION_LOST') {
          console.log('Retrying connection in 2 seconds...');
          setTimeout(() => {
            getConnection().then(resolve).catch(reject);
          }, 2000);
        } else {
          reject(err);
        }
      } else {
        // Set session variables to increase timeouts
        connection.query('SET SESSION wait_timeout=300', (err) => {
          if (err) console.error('Error setting wait_timeout:', err);

          connection.query('SET SESSION interactive_timeout=300', (err) => {
            if (err) console.error('Error setting interactive_timeout:', err);
            resolve(connection);
          });
        });
      }
    });
  });
};

// Function to execute a query with retry logic
const executeQuery = async (sql, params, retries = 3) => {
  let connection;
  try {
    connection = await getConnection();
    const query = util.promisify(connection.query).bind(connection);
    return await query(sql, params);
  } catch (error) {
    if (retries > 0 && (error.code === 'PROTOCOL_CONNECTION_LOST' ||
      error.code === 'PROTOCOL_ENQUEUE_AFTER_FATAL_ERROR' ||
      error.message.includes('timeout'))) {
      console.log(`Query failed, retrying... (${retries} attempts left)`);
      await new Promise(resolve => setTimeout(resolve, 2000)); // Wait 2 seconds
      return executeQuery(sql, params, retries - 1);
    }
    throw error;
  } finally {
    if (connection) connection.release();
  }
};

// Process records in batches
async function run(row) {

  // if (row.Kabupaten < 'KOTA MAGELANG') return

  // console.log(row.NIK, row.Kabupaten, row.Kecamatan, row.Desa, row.TipeData);

  const recs1 = await executeQuery(`select * FROM tmp_1022 where NIK = '${row.NIK}'`);
  if (recs1.length) {
    console.log('Data already exists '+recs1[0].Intervensi)
    if(recs1[0].Intervensi && !recs1[0].Intervensi.match(/Diluar Prioritas/i)) return

    await executeQuery(`UPDATE tmp_1022 SET Intervensi = '${row.Intervensi}' WHERE NIK = '${recs1[0].NIK}'`)
    return
  }

  const recs2 = await executeQuery(`select * FROM tmp_1022 where NIK2 = '${row.NIK}'`);
  if (recs2.length) {
    console.log('Data already exists on NIK2 '+recs2[0].Intervensi)
    if(recs2[0].Intervensi && !recs2[0].Intervensi.match(/Diluar Prioritas/i)) return

    await executeQuery(`UPDATE tmp_1022 SET Intervensi = '${row.Intervensi}' WHERE NIK2 = '${recs2[0].NIK}'`)
    return
  }

  const recs3 = await executeQuery(`select * FROM tmp_1022 where NIK3 = '${row.NIK}'`);
  if (recs3.length) {
    console.log('Data already exists on NIK3 '+recs3[0].Intervensi)
    if(recs3[0].Intervensi && !recs3[0].Intervensi.match(/Diluar Prioritas/i)) return

    await executeQuery(`UPDATE tmp_1022 SET Intervensi = '${row.Intervensi}' WHERE NIK2 = '${recs3[0].NIK}'`)
    return
  }

  // get data that has no intervention
  const recs = await executeQuery(`select * FROM tmp_1022
    where Intervensi IS NULL AND NIK2 IS NULL AND NIK3 IS NULL AND Kabupaten = '${row.Kabupaten}'
    LIMIT 1`);
  
  if (!recs.length) {
    console.log('Data not found\n')
    return
  }

  const rec = recs[0]
  console.log('Switch to: ', rec.NIK, rec.Nama);
  // let query = `UPDATE tmp_1022 SET NIK2 = ${row.NIK}, Intervensi = 'APBD PROV 2025', NIKENC2 = AES_ENCRYPT('${row.NIK}', '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5') 
  //   WHERE NIK = '${rec.NIK}'`
  let query = `UPDATE tmp_1022 SET NIK3 = ${row.NIK}, Intervensi3 = '${row.Intervensi}', NIKENC3 = AES_ENCRYPT('${row.NIK}', '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5') 
    WHERE NIK = '${rec.NIK}'`
  await executeQuery(query)

  return null;
}

async function run2 (kab) {
  await executeQuery(`update tmp_1022 set Ordr = NULL
    where Kabupaten = '${kab.Kabupaten}'`);
  
  await executeQuery(`update tmp_1022 t 
    join (
      select 
        ROW_NUMBER() OVER (PARTITION BY Kabupaten ORDER BY Kabupaten) RowNum,
        NIKENC, Kabupaten
      from tmp_1022
      where Kabupaten = '${kab.Kabupaten}' 
      AND desil IS NULL
    ) tx
    on t.NIKENC = tx.NIKENC
    SET t.Ordr = tx.RowNum`
  )
  // -- AND COALESCE(Intervensi, Intervensi2, Intervensi3) IS NULL

  let sql = `update tmp_1022 t 
      join (
        select
          ROW_NUMBER() OVER (ORDER BY 1) Ordr,
          td.nik NIK, AES_ENCRYPT(td.nik, '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5') NIKENC, td.nmkab Kabupaten, td.Intervensi, isUsed, td.desil_dtsen
        from tmp_dtse td 
        where td.nmkab = '${kab.Kabupaten}' AND isUsed IS NULL AND LENGTH(nik) = 16 AND td.tipe_data = 'rtlh'
        ) tx
      on t.Ordr = tx.Ordr
      and t.Kabupaten = tx.Kabupaten
    set t.NIK3 = tx.NIK, t.NIKENC3 = tx.NIKENC, t.desil = tx.desil_dtsen, t.Intervensi3 = tx.Intervensi
    where t.Kabupaten = '${kab.Kabupaten}' AND tx.isUsed IS NULL
    -- AND COALESCE(t.Intervensi, t.Intervensi2, t.Intervensi3) IS NULL 
    AND t.NIKENC3 IS NULL AND desil IS NULL`
  let records = await executeQuery(sql);
  console.log(records.affectedRows)
  
  // await executeQuery(`update tmp_310 t
  //   join tmp_dtse d
  //   on t.nik = d.nik 
  //   set t.desil = d.desil_dtsen
  //   where t.kabupaten = '${kab.Kabupaten}'`)

  // await executeQuery(`update tmp_310 set Ordr = NULL
  //   where kabupaten = '${kab.Kabupaten}'`);
  
  // let records = await executeQuery(`update tmp_310 t 
  //   join (
  //     select 
  //       ROW_NUMBER() OVER (PARTITION BY kabupaten ORDER BY kabupaten) RowNum,
  //       nik, kabupaten
  //     from tmp_310
  //     where kabupaten = '${kab.Kabupaten}' 
  //     AND desil IS NULL
  //   ) tx
  //   on t.nik = tx.nik
  //   SET t.Ordr = tx.RowNum`
  // )
  // // -- AND COALESCE(Intervensi, Intervensi2, Intervensi3) IS NULL
  // // console.log(records.affectedRows)

  // let sql = `update tmp_310 t 
  //     join (
  //       select 
  //         ROW_NUMBER() OVER (ORDER BY 1) Ordr, 
  //         td.nik NIK, td.NIKENC, td.nmkab Kabupaten, td.Intervensi, td.desil_dtsen 
  //       from tmp_dtse td  
  //         left join tmp_310 tt
  //         on td.nik = tt.nik
  //       where td.tipe_data = 'backlog' AND td.nmkab = '${kab.Kabupaten}' AND tt.nik IS NULL
  //       LIMIT ${records.affectedRows}
  //       ) tx
  //     on t.Ordr = tx.Ordr
  //     and t.kabupaten = tx.Kabupaten
  //   set t.NIK3 = tx.NIK, t.NIKENC3 = tx.NIKENC, t.desil = tx.desil_dtsen, t.Intervensi3 = tx.Intervensi 
  //   where t.kabupaten = '${kab.Kabupaten}' AND t.NIKENC3 IS NULL AND t.desil IS NULL`
  // executeQuery(sql);
  // console.log(records.affectedRows)
  
  // Wait for 10 minutes (600,000 milliseconds)
  // await new Promise(resolve => setTimeout(resolve, 500000));
}

async function main() {
  try {

    // await readCSV()

    const kabupaten = await executeQuery(`select aa.AreaName Kabupaten from arch_area aa 
      where aa.ParentAreaID = 33`);

    for (const kab of kabupaten) {
      console.log(kab.Kabupaten)

      await run2(kab)
    }

    console.log('processed successfully');

    // End MySQL pool
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(0);
    });

    process.exit(0)
  } catch (error) {
    console.error('Error:', error);

    // End MySQL pool on error
    pool.end(err => {
      if (err) console.error('Error closing MySQL pool:', err);
      process.exit(1);
    });
  }
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('Process interrupted, closing connections...');
  pool.end(err => {
    if (err) console.error('Error closing MySQL pool:', err);
    process.exit(2);
  });
});

main();