<template>
  <Modal title="DETAIL BENCANA" :show.sync="xshow" @onSubmit="Save">
    <template v-slot:left-action>
      <v-btn v-show="showBNBA" text color="red" @click="showBNBA = false"
        >DATA BENCANA</v-btn
      >
    </template>
    <div>
      <div class="iblock" style="width: 500px" v-show="!showBNBA">
        <Map
          width="480px"
          height="330px"
          :lat.sync="forms.GeoLat"
          :lon.sync="forms.GeoLng"
        />
      </div>
      <div class="iblock form-inline" v-show="!showBNBA">
        <XInput label="Tahun" type="number" :value.sync="forms.Tahun" />
        <XSelect
          label="Jenis Bencana"
          dbref="BCN.SelBencana"
          :value.sync="forms.BencanaID"
          :text.sync="forms.Bencana"
        />
        <XSelect
          label="Kabupaten"
          dbref="Arch.SelArea"
          :value.sync="forms.KabupatenID"
          :text.sync="forms.Kabupaten"
          :dbparams="{ ParentAreaID: 33 }"
        />
        <XSelect
          label="Kecamatan"
          dbref="Arch.SelArea"
          :value.sync="forms.KecamatanID"
          :text.sync="forms.Kecamatan"
          :dbparams="{ ParentAreaID: forms.KabupatenID }"
        />
        <XSelect
          label="Kelurahan"
          dbref="Arch.SelArea"
          :value.sync="forms.KelurahanID"
          :text.sync="forms.Kelurahan"
          :dbparams="{ ParentAreaID: forms.KecamatanID }"
        />
        <XInput label="Rumah Rusak" :value.sync="forms.RumahRusak" />
        <XInput label="Fasum Rusak" :value.sync="forms.FasumRusak" />
        <br />
        <v-btn
          @click="showBNBA = true"
          color="primary"
          style="width: 100%"
          text
        >
          Tampilkan BNBA
        </v-btn>
      </div>
      <br />
      <Grid
        :datagrid.sync="bnba"
        dbref="BCN.BencanaBNBA"
        :dbparams="forms"
        style="height: 400px"
        class="dense"
        v-show="showBNBA"
        :columns="[
          {
            name: 'NIK',
            value: 'NIK',
            width: '130px',
          },
          {
            name: 'Nama',
            value: 'Nama',
            width: '170px',
          },
          {
            name: 'Alamat',
            value: 'Alamat',
            width: '265px',
          },
          {
            name: 'IDBDT',
            value: 'IDBDT',
            width: '130px',
          },
        ]"
      >
      </Grid>
    </div>
  </Modal>
</template>
<script>
export default {
  data: () => ({
    forms: {},
    bnba: [],
    xshow: false,
    showBNBA: false,
    mapValue: null,
  }),
  props: {
    id: {
      type: [Number, String],
      default: 0,
    },
    show: {
      type: Boolean,
      default: false,
    },
    geoLoc: {
      type: String,
      default: null,
    },
  },
  watch: {
    id(val) {
      this.populate(val)
    },
    geoLoc(val) {
      if (val) {
        if (!this.id) {
          // console.log(val)
          this.forms.GeoLat = val.split('|')[0]
          this.forms.GeoLng = val.split('|')[1]
        }
      }
    },
    save() {
      this.onSave()
    },
    show(val) {
      this.xshow = val
    },
    xshow(val) {
      if (!val) this.forms = {}
      else if (this.xshow) this.populate()
      this.$emit('update:show', val)
    },
  },
  computed: {
    kecamatanParams() {
      return { ParentAreaID: this.forms.KabupatenID }
    },
  },
  methods: {
    async populate(id) {
      if (id) {
        let { data } = await this.$api.call('BCN.SelKebencanaan', {
          KebencanaanID: this.id,
        })
        this.forms = data[0]
      } else {
        this.forms = {}
      }
    },
    async Save() {
      await this.$api.call('BCN.SavKebencanaan', {
        ...this.forms,
      })
      this.xshow = false
    },
  },
}
</script>
