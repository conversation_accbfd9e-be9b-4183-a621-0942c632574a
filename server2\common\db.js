const mysql = require('mysql')
const util = require('util')
const moment = require('moment')
const fs = require('fs')
const dbconfig = require('./dbconfig')

const pool = mysql.createPool(dbconfig)

pool.query = util.promisify(pool.query) // Magic happens here.


let queue = []
// Implement a size-limited cache with automatic cleanup
const MAX_CACHE_SIZE = 1000; // Maximum number of items in cache
const CACHE_TTL = 5 * 60 * 1000; // Cache TTL: 5 minutes in milliseconds
let cache = {}
let cacheKeys = []; // Track keys in insertion order for LRU eviction

// Function to add item to cache with TTL
function addToCache(key, value) {
  // If cache is full, remove oldest item (LRU)
  if (cacheKeys.length >= MAX_CACHE_SIZE) {
    const oldestKey = cacheKeys.shift();
    delete cache[oldestKey];
  }

  // Add new item to cache
  cache[key] = {
    value,
    timestamp: Date.now()
  };
  cacheKeys.push(key);

  // Set expiration
  setTimeout(() => {
    if (cache[key]) {
      delete cache[key];
      const keyIndex = cacheKeys.indexOf(key);
      if (keyIndex !== -1) {
        cacheKeys.splice(keyIndex, 1);
      }
    }
  }, CACHE_TTL);
}

// Function to get item from cache
function getFromCache(key) {
  const item = cache[key];
  if (!item) return null;

  // Check if item is expired
  if (Date.now() - item.timestamp > CACHE_TTL) {
    delete cache[key];
    const keyIndex = cacheKeys.indexOf(key);
    if (keyIndex !== -1) {
      cacheKeys.splice(keyIndex, 1);
    }
    return null;
  }

  return item.value;
}

// Periodically clean up expired cache items (every 10 minutes)
setInterval(() => {
  const now = Date.now();
  const expiredKeys = [];

  // Find expired keys
  for (const key in cache) {
    if (now - cache[key].timestamp > CACHE_TTL) {
      expiredKeys.push(key);
    }
  }

  // Remove expired items
  expiredKeys.forEach(key => {
    delete cache[key];
    const keyIndex = cacheKeys.indexOf(key);
    if (keyIndex !== -1) {
      cacheKeys.splice(keyIndex, 1);
    }
  });

  if (expiredKeys.length > 0) {
    console.log(`Cleaned up ${expiredKeys.length} expired cache items`);
  }
}, 10 * 60 * 1000);

const sleep = ms => new Promise(res => setTimeout(res, ms));
function regQueue(sp) {
  let id = sp + moment().valueOf()
  queue.push(id)
  // console.log(queue)
  setTimeout(() => {
    var idx = queue.indexOf(id);
    if (idx !== -1) {
      queue.splice(idx, 1);
    }
  }, 20000)
  return id
}
async function waitForQueue(id) {
  let to = 0
  while (queue.length && queue[0] != id) {
    await sleep(500)
    console.log(`wait ${id}:${queue.length}:${queue[0]}`)
    to++
    if (to > 40) {
      console.log(`timeout ${id}:${queue.length}`)
      var idx = queue.indexOf(id);
      if (idx !== -1) {
        queue.splice(idx, 1);
      }
      return "Timeout Exceeded"
    }
  }
}
function releaseQueue(id) {
  // console.log(`release: ${id}`);
  var idx = queue.indexOf(id);
  if (idx !== -1) {
    queue.splice(idx, 1);
  }
}

async function checkDeadlock() {
  const dbp = await pool.query(
    `SHOW ENGINE INNODB STATUS`
  )
  let texts = dbp[0].Status.split('\n')

  // const plist = await pool.query(
  //   `SHOW PROCESSLIST`
  // )

  let hasDeadlock = false
  for (let t of texts) {
    let l = t.match(/TRANSACTION \d+, ACTIVE (\d+) sec/)
    if (l && parseInt(l[1]) > 5) {
      hasDeadlock = true
    } else {
      let m = t.match(/MySQL thread id (\d+)/)
      if (hasDeadlock && m) {
        let atx = moment().format('DDMMHHmm');
        fs.writeFileSync(`tmp/deadlog${atx}`, dbp[0].Status)
        try {
          const dbp = await pool.query(
            `KILL ${m[1]}`
          )
          hasDeadlock = false
        } catch (ex) {
          fs.writeFileSync(`tmp/failed${atx}`, 'failed')
          hasDeadlock = true
        }
      }
    }
  }
  return hasDeadlock
}

const fileUtils = {
  async getFile(checksum) {
    const result = await pool
      .query(
        `SELECT * FROM arch_uploads
        WHERE Checksum = UNHEX('${checksum}')`
      )
      .catch(err => {
        console.error(`Error on getFile(${checksum}): ${err.message}`)
        return null
      })

    return result
  },
  async insertFile(url, checksum, filename, exifData, userId) {
    if (process.env.NODE_ENV === 'development') return

    const sql = `INSERT INTO arch_uploads (Url, Checksum, Filename, ExifData, CreatedAt, CreatedBy)
      VALUES ('${url}', UNHEX('${checksum}'), ${mysql.escape(filename)}, ${mysql.escape(exifData)}, NOW(), ${userId || 0})`
    await pool
      .query(sql)
      .catch(err => {
        console.error(sql)
        console.error(`Error on insertFile: ${err.message}`)
      })

    return this.getFile(checksum)
  },
  async markFile(url, markType, markValue) {
    const sql = `UPDATE arch_uploads SET
        IsUsed = CASE WHEN '${markType}' = 'isUsed' THEN ${markValue} ELSE IsUsed END,
        IsArchived = CASE WHEN '${markType}' = 'isArchived' THEN ${markValue} ELSE IsArchived END
      WHERE Url = '${url}'`
    await pool
      .query(sql)
      .catch(err => {
        console.error(sql)
        console.error(`Error on markFile: ${err.message}`)
        return null
      })
  },
  async markFiles(urls, markType, markValue, userId, sp) {
    const sql = `UPDATE arch_uploads SET
        IsUsed = CASE WHEN '${markType}' = 'isUsed' THEN ${markValue} ELSE IsUsed END,
        IsArchived = CASE WHEN '${markType}' = 'isArchived' THEN ${markValue} ELSE IsArchived END,
        CreatedBy = CASE WHEN COALESCE(CreatedBy,0) = 0 THEN ${userId || 0} END,
        ExifData = CASE WHEN ExifData IS NULL THEN '{"sp":"${sp}"}' 
          WHEN ExifData LIKE '%sp%' THEN ExifData
          ELSE CONCAT(LEFT(ExifData, LENGTH(ExifData) - 1), ', "sp":"${sp}"}') END
      WHERE Url IN ('${urls.join("','")}')`
    // console.log(sql)
    await pool
      .query(sql)
      .catch(err => {
        console.error(sql)
        console.error(`Error on markFile: ${err.message}`)
        return null
      })
  },
}

async function aes_decrypt(encryptedData, key) {
  const ret = await pool.query(`CALL Arch_SelDecrypt(?, ?)`, [encryptedData, key])
  return ret[0][0].Content
  // try {
  //   // MySQL's AES functions use a 128-bit key.
  //   // We need to ensure our key is exactly 16 bytes (128 bits).
  //   const keyBuffer = Buffer.alloc(16, 0); // Create a 16-byte buffer filled with zeros
  //   keyBuffer.write(key, 0, 'utf8'); // Write the key string to the buffer

  //   // MySQL uses the 'aes-128-ecb' cipher.
  //   // ECB mode does not use an Initialization Vector (IV), so we pass an empty string or null.
  //   const decipher = crypto.createDecipheriv('aes-128-ecb', keyBuffer, null);

  //   // It's important to disable auto padding as MySQL handles padding itself.
  //   decipher.setAutoPadding(false);

  //   // Decrypt the data
  //   let decrypted = decipher.update(encryptedData, 'binary', 'utf8');
  //   decrypted += decipher.final('utf8');

  //   // MySQL pads with null bytes, which we should trim.
  //   return decrypted.replace(/\0+$/, '');

  // } catch (error) {
  //   console.error("Decryption failed:", error);
  //   return null;
  // }

}

async function aes_encrypt(content, key) {
  const ret = await pool.query(`CALL Arch_SelEncrypt(?, ?)`, [content, key])
  return ret[0][0].Content
}

const ENCKEY = '$xHgFGUrK@hn$!2RH&MQjnkEVhmx5'
async function buildQuery(spName, param, mode = 'sp') {
  let dbp = []
  if (mode == 'sp')
    dbp = await pool.query(
      `SELECT * FROM information_schema.PARAMETERS
      WHERE SPECIFIC_SCHEMA = '${dbconfig.database}' AND SPECIFIC_NAME = '${spName}' AND PARAMETER_MODE = 'IN'`
    )
  else
    dbp = await pool.query(
      `SELECT * FROM information_schema.COLUMNS
      WHERE SPECIFIC_SCHEMA = '${dbconfig.database}' AND TABLE_NAME = '${spName}'`
    )

  const rgxs = {
    _NIK: '^[0-9]{16}$',
    _Phone: '^(0|62|\\+62)[0-9-]+$',
    _Email: '^[A-Z0-9._%+-]+@[A-Z0-9.-]+.[A-Z]{2,4}$'
  }

  let sql = 'CALL ' + spName + '('
  let sqlTable = 'REPLACE INTO ' + spName + ' ('
  let i = 0
  let error = null
  const runParam = []
  const uploadUrls = []

  for (const idx in dbp) {
    let paramName = dbp[idx].PARAMETER_NAME // .substr(1);
    if (mode == 'table') paramName = dbp[idx].COLUMN_NAME
    let paramValue = param[paramName] !== null && param[paramName] !== undefined ? param[paramName] : param[paramName.replace(/^_/, '')]
    //const paramValue = param[paramName] || param[paramName.replace(/^_/, '')]
    // console.log(dbp[idx].DATA_TYPE)
    // console.log(paramName, dbp[idx].DATA_TYPE, paramValue)
    if (paramName === '_UserIDRef') {
      if (param._userId) {
        sql += '?'
        runParam.push(param._userId)
      } else {
        error = `DB Not authorized`
      }
    } else if (paramName === '_RoleIDRef') {
      sql += '?'
      runParam.push(param._roleId)
    } else if (paramName === '_EncKey') {
      sql += '?'
      runParam.push(ENCKEY)
    } else if (paramName === '_DeviceIdRef') {
      sql += '?'
      runParam.push(param._deviceId)
    } else if (paramName === '_IpAddrRef') {
      sql += '?'
      runParam.push(param._ip)
    } else if (paramValue !== null && paramValue !== undefined) {
      if (paramValue.trim) paramValue = paramValue.trim()
      if (paramValue && paramValue.match &&
        ((paramValue.match(/</) && paramValue.match(/>/)) ||
          paramValue.match(/--/) ||
          (rgxs[paramName] && !paramValue.match(new RegExp(rgxs[paramName], 'i')))
        ) && spName.match(/(Sav|Upd|Del)/)) {
        error = `invalid input for ${paramName.replace(/^_/, '')}`
      }
      if (paramName.substr(0, 4) === '_Xml') {
        // $sql += `<xml>.str_replace("'", "", str_replace("{", "<'", str_replace("}", ">'", $_VAR[$dbp['PARAMETER_NAME']]))).</xml>`;
        sql += '?'
        runParam.push(
          `<xml>${paramValue.replace(/{/gi, '<').replace(/}/gi, '>')}</xml>`
        )
      } else if (paramValue === 'undefined') {
        sql += '?'
        runParam.push(null)
      } else if (dbp[idx].DATA_TYPE == 'varbinary') {
        if (paramValue.type === 'Buffer') {
          sql += '?'
          runParam.push(Buffer.from(paramValue.data))
        } else if (paramValue) {
          sql += '?'
          const encrypted = await aes_encrypt(paramValue, ENCKEY)
          console.log(encrypted)
          runParam.push(Buffer.from(encrypted))
        } else {
          sql += '?'
          runParam.push(null)
        }
      } else if (paramValue.type === 'Buffer' && dbp[idx].DATA_TYPE !== 'varbinary') {
        error = `invalid input for ${paramName.replace(/^_/, '')}`
      } else if (
        paramValue === '' &&
        (dbp[idx].DATA_TYPE !== 'varchar' || dbp[idx].DATA_TYPE !== 'text')
      ) {
        sql += '?'
        runParam.push(null)
      } else if (['int', 'bigint', 'decimal'].includes(dbp[idx].DATA_TYPE)) {
        let m = null
        if (paramValue.match)
          m = paramValue.match(/[^\d.,-]/)
        if (m) {
          error = `${dbp[idx].PARAMETER_NAME} tidak boleh ada karakter " ${m[0]} "`
        } else {
          sql += '?'
          runParam.push(paramValue === '-' ? null : paramValue)
        }
      } else if (dbp[idx].DATA_TYPE === 'date') {
        if (!paramValue.match(/^\d/)) {
          sql += '?'
          runParam.push(null)
        } else if (!paramValue.match(/^(\d{4}-\d{2}-\d{2}|\d{2}-\d{2}-\d{4}|\d{2}-[A-Za-z]{3}-\d{4})$/)) {
          error = `${dbp[idx].PARAMETER_NAME} salah format " ${paramValue} "`
        } else {
          // console.log('yy', paramValue)
          // sql += '?'
          // runParam.push(moment(paramValue).format('YYYY-MM-DD'))
          sql += '?'
          runParam.push(paramValue)
        }
      } else {
        sql += '?'
        runParam.push(paramValue)
      }

      if (typeof (paramValue) == 'string' && paramValue.match(/^\/?uploads\//)) {
        const vurl = paramValue.replace(/^\/?uploads\//, '')
        uploadUrls.push(vurl)
        // fileUtils.markFile(vurl, 'isUsed', 1, param._userId)
      }
    } else {
      sql += '?'
      runParam.push(null)
    }

    if (paramValue) {
      sqlTable += paramName + ','
    }
    // console.log(dbp[idx].DATA_TYPE, runParam[runParam.length-1])
    i++
    if (i < dbp.length) sql += ','
    if (error) break
  }
  sql += ')'
  if (error) {
    return {error}
  }

  if (uploadUrls.length) {
    fileUtils.markFiles(uploadUrls, 'isUsed', 1, param._userId, spName)
  }

  return {sql: sql, params: runParam}
}

// Memory monitoring function
function monitorMemoryUsage() {
  const memoryUsage = process.memoryUsage();
  const memoryUsageMB = {
    rss: Math.round(memoryUsage.rss / 1024 / 1024),
    heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024),
    heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024),
    external: Math.round(memoryUsage.external / 1024 / 1024)
  };

  console.log('Memory Usage (MB):', memoryUsageMB);

  // If memory usage is high, force garbage collection if possible
  if (memoryUsageMB.heapUsed > 1024) { // More than 1GB
    console.log('High memory usage detected, attempting cleanup...');

    // Clear caches
    cache = {};
    cacheKeys = [];

    // Force garbage collection if node was started with --expose-gc
    if (global.gc) {
      global.gc();
      console.log('Garbage collection completed');
    } else {
      console.log('To enable garbage collection, start node with --expose-gc flag');
    }
  }
}

// Monitor memory usage every 5 minutes
setInterval(monitorMemoryUsage, 5 * 60 * 1000);

// Function to safely handle file operations with proper cleanup
function safeFileOperation(operation, errorMessage) {
  return async (...args) => {
    try {
      return await operation(...args);
    } catch (error) {
      console.error(errorMessage, error);
      // Attempt to clean up any resources
      return null;
    }
  };
}

module.exports = {
  ...fileUtils,
  classifyError(err, sp, params) {
    let errMsg = 'Error on API Call'
    let errVal = ''
    const msg = err.message
    let match = msg.match(/Data too long for column '_?([^\n]+)'/i)
    if (match) {
      errMsg = `Error: "${match[1]}" terlalu panjang`
    }
    if (!match) {
      match = msg.match(/Data truncated for column '_?([^\n]+)'/i)
      if (match) {
        errMsg = `Error: "${match[1]}" terlalu panjang`
      }
    }
    if (!match) {
      match = msg.match(/Column '_?([^\n]+)' cannot be null/i)
      if (match) {
        errMsg = `Error: "${match[1]}" harus diisi`
      }
    }
    if (!match) {
      const match = msg.match(/column '_?([^\n]+)'/i)
      if (match) errMsg = `Error: "${match[1]}" salah / harus diisi`
    }
    if (!match) {
      const match = msg.match(/column .{7}_?([^\n]+)`/i)
      if (match) errMsg = `Error: "${match[1]}" salah format`
    }
    if (!match) {
      match = msg.match(/Duplicate entry/)
      if (match) errMsg = 'Data sudah ada'
    }
    if (match && match[1]) {
      console.log(match, params)
      errVal = params[match[1]] || params['_' + match[1]]
    }
    if (!match && msg.match(/LOCK_WAIT_TIMEOUT/)) {
      errMsg = 'Server sedang penuh, coba beberapa saat lagi'
    }
    console.error(`Error on API CALL: (${sp}): ${msg} => ${errVal}`)
    if (err.code === 'ER_PARSE_ERROR') {
      console.error(err.sql)
    }
    return {
      ErrCode: 100,
      Message: errMsg,
      Error: `(${sp}) ` + msg
    }
  },
  checkDeadlock: checkDeadlock,
  esc: mysql.escape,
  exec: async function (sp, params, opts = {}) {
    let start = moment()
    sp = sp.replace(/[^\w_.]/gi, '')

    const query = await buildQuery(sp, params)
    if (query.error) {
      return [
        {
          ErrCode: 100,
          Message: query.error,
          Error: query.error
        }
      ]
    }
    let qid = null
    /*if(sp.match(/_Sav/)) {
      qid = regQueue(sp)
      let err = await waitForQueue(qid)
      if(err) {
        return [
          [
            {
              ErrCode: 100,
              Message: `Timeout Exceeded`,
              Error: `Timeout Exceeded`
            }
          ]
        ]
      }
    }*/
    let key = JSON.stringify(query)
    let result = null

    // Try to get from cache first
    result = getFromCache(key);

    if (!result) {
      // Not in cache or expired, query the database
      try {
        result = await pool.query(query.sql, query.params);

        // Log activities for write operations
        if (sp.match(/Sav|Upd|Del|Rpt/) && !sp.match(/SavOTP/) && !sp.match(/EVO_Rpt/)) {
          let keyVal = null
          if (params.NIK || params._NIK) {
            keyVal = params.NIK || params._NIK
            if(keyVal.type == 'Binary') {
              keyVal = Buffer.from(keyVal.data)
            }
          }
          let px = query.params.map(v => v == ENCKEY ? '_EncKey' : JSON.stringify(v).replace(/"/g, '') )
          let inssql = `INSERT INTO log_activities (CreatedAt, Source, UserId, KeyVal, PageRef, Details)
            VALUES (NOW(), 'BE', ${params._userId || -1}, ?, "${sp}(${mysql.escape(px.join(","))})", NULL)`
          pool.query(inssql, [keyVal])
            .catch(logErr => {
              console.error('Error logging activity:', logErr.message);
              console.log(inssql)
            });
        }

        // Cache the result if it's not too large and it's a read operation
        // if (key.length < 500 && !sp.match(/Sav|Upd|Del/) && result.length > 1) {
        //   addToCache(key, result);
        // }
      } catch (err) {
        const errObj = module.exports.classifyError(err, sp, query.params);
        result = [[errObj]];
      }
    }

    // if(qid) releaseQueue(qid)
    let delta = moment().diff(start, 'second')
    if (delta > 5 && !sp.match(/Rpt/)) {
      // console.error(`${sp} took ${delta} secs`)
    }
    // console.log(moment().diff(start, 'second'))
    if (opts.returnAll) {
      return result
    }
    if (result.length > 2) {
      if (result[0].length && result[0][0]._Settings) {
        return result
      }
      return result[result.length - 2]
    } else return result[0]
  },
  async getColumns(table) {
    const result = await pool
      .query(
        `SELECT ORDINAL_POSITION, COLUMN_NAME, DATA_TYPE, COLUMN_TYPE, IS_NULLABLE FROM information_schema.COLUMNS c
        WHERE TABLE_NAME = '${table}'`
      )
      .catch(err => {
        console.error(`Error on getColumns(${table}): ${err.message}`)
        return null
      })

    return result
  },
  async spHasPagination(spName) {
    const dbp = await pool.query(
      `SELECT * FROM information_schema.PARAMETERS
      WHERE SPECIFIC_SCHEMA = '${dbconfig.database}' AND SPECIFIC_NAME = '${spName}' AND PARAMETER_MODE = 'IN'`
    )
    let has = false
    for (const idx in dbp) {
      if (dbp[idx].PARAMETER_NAME === '_PageRef') {
        has = true
        break
      }
    }
    return has
  },
  async query(sql, params) {
    const result = await pool.query(sql, params).catch(err => {
      console.error(sql, params)
      console.error(`Error on Query: ${err.message}`)
      return [[{ErrCode: 100, Message: 'Error on Query', Error: err.message}]]
    })
    return result
  },
  pool: pool,
  dbname: dbconfig.database
}
