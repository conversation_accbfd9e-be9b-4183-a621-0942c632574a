<template>
  <Page title="Progress KKN">
    <template v-slot:toolbar>
      <v-btn text :loading="loadingPrint" small @click="Download">
        <v-icon>mdi-microsoft-excel</v-icon>
      </v-btn>
    </template>
    <div style="display: flex">
      <XSelect
        placeholder="Pilih Universitas"
        dbref="KKN.SelUniversity"
        :dbparams="{}"
        :value.sync="university"
        width="190px"
      />
    </div>
    <Grid
      :datagrid.sync="progress"
      dbref="KKN.Progress"
      :dbparams="dbparams"
      style="height: calc(100vh - 150px)"
      :disabled="true"
      class="dense"
      :columns="[
        {
          name: 'Universitas / Nama Mhs.',
          value: 'Nama',
          width: '200px',
        },
        {
          name: 'Total',
          value: 'Total',
          class: 'right',
        },
        {
          name: '<PERSON>gkap',
          value: '<PERSON>gkap',
          class: 'right',
        },
        {
          name: '<PERSON><PERSON>',
          value: 'BelumLengkap',
          class: 'right',
        },
      ]"
    >
    </Grid>
  </Page>
</template>
<script>
export default {
  components: {
    // NikBlock,
  },
  data: () => ({
    roleAccess: false,
    showRolesPage: false,
    showAreaAccess: false,
    progress: [],
    rolesData: [],
    areaAccessData: [],
    university: null,
    userId: null,
    users: null,
    kabupaten: null,
    kabupatenId: null,
    kecamatan: null,
    forms: {},
    loadingPrint: false,
  }),
  computed: {
    dbparams() {
      return {
        University: this.university,
      }
    },
  },
  async mounted() {},
  methods: {
    async Download() {
      this.loadingPrint = true
      let ret = await this.$api.post(`/report/Generic/xlsx`, {
        sp: `KKN_RptProgress`,
      })
      this.loadingPrint = false
      this.$api.download(this.$api.url + '/report' + ret.data)
    },
  },
}
</script>
<style lang="scss">
#modal-role-access {
  .ui-table {
    height: 500px !important;
  }
}
</style>
