<template>
  <div v-tooltip="'REKOM ' + rekom" class="s-REKOM" :class="'s-REKOM' + rekom">
    {{ rekom }}
  </div>
</template>
<script>
export default {
  props: {
    rekom: String,
  },
}
</script>
<style lang="scss">
.s-REKOM {
  font-size: 10px !important;
  padding: 1px 3px;
  border-radius: 3px;
  min-width: 18px;
  text-align: center;
  border: 1px solid silver;
  margin-right: 5px;
}
.s-REKOM1 {
  background: #c0ca33 !important;
  color: white !important;
  // background: #e6194b;
  // color: white;
  font-size: 18px;
}
.s-REKOM2 {
  background: #ff6f00 !important;
  color: white !important;
  font-size: 18px;
}
.s-REKOM3 {
  background: #9c21b5 !important;
  color: white !important;
  font-size: 18px;
}
.s-REKOM4 {
  background: #2194f7 !important;
  color: white !important;
  font-size: 18px;
}
.s-REKOM5 {
  background: yellow !important;
  color: #333 !important;
  font-size: 18px;
}
.s-REKOM6 {
  background: #42a3a7 !important;
  color: white !important;
  font-size: 18px;
}
.s-REKOM7 {
  background: #eef7fb !important;
  color: #333 !important;
  font-size: 18px;
}
.s-REKOM8 {
  background: #42526e !important;
  color: white !important;
  font-size: 18px;
}
.s-REKOM9 {
  background: #7b524a !important;
  color: white !important;
  font-size: 18px;
}
.s-REKOM10 {
  background: #4aad52 !important;
  color: white !important;
  font-size: 18px;
}
</style>
